'use client';
import { useState } from 'react';
import { sendGTMEvent } from '@next/third-parties/google';
import clsx from 'clsx';
import { useRouter } from 'next/navigation';
import LoadingOverlay from '@/components/containers/itemForm/LoadingOverlay';
import ShopPublicImage from '@/components/ShopImage';
import { ITEM_TYPE, ItemTypeString, ItemTypeValue } from '@/types/item';

type MenuItem = {
  id: ItemTypeValue;
  itemType: ItemTypeString;
  itemTypeValue: number;
  title: string;
  description: string;
  srcIcon: string;
  src: string;
  alt: string;
  widthIcon?: number;
  heightIcon?: number;
  width?: number;
  height?: number;
};

type MenuItemCardProps = {
  isChekiExhibitable: boolean;
  identityId: string;
};
const menuItems = (isChekiExhibitable: boolean): MenuItem[] => [
  {
    id: ITEM_TYPE.DIGITAL_BUNDLE.value,
    itemType: ITEM_TYPE.DIGITAL_BUNDLE.str,
    itemTypeValue: ITEM_TYPE.DIGITAL_BUNDLE.value,
    title: 'デジタルコンテンツ',
    description: '画像/動画/音声を販売',
    srcIcon: '/images/iconDigitalContents.webp',
    src: '/shop/images/mockDigitalContents.webp',
    alt: 'Digital Contents',
    widthIcon: 112,
    heightIcon: 32,
    width: 169,
    height: 99,
  },
  ...[
    {
      id: ITEM_TYPE.DIGITAL_GACHA.value,
      itemType: ITEM_TYPE.DIGITAL_GACHA.str,
      itemTypeValue: ITEM_TYPE.DIGITAL_GACHA.value,
      title: 'デジタルガチャ',
      description: '画像/動画/音声をガチャで販売',
      srcIcon: '/images/iconDigitalContents.webp',
      src: '/shop/images/mockDigitalGacha.webp',
      alt: 'Digital Gacha',
      widthIcon: 112,
      heightIcon: 32,
      width: 120,
      height: 120,
    },
  ],
  ...(isChekiExhibitable
    ? [
        {
          id: ITEM_TYPE.CHEKI.value,
          itemType: ITEM_TYPE.CHEKI.str,
          itemTypeValue: ITEM_TYPE.CHEKI.value,
          title: 'チェキ風ブロマイド',
          description: '画像をチェキ風ブロマイドとして販売',
          srcIcon: '/images/iconCheki.webp',
          src: '/shop/images/mockCheki.webp',
          alt: 'Cheki',
          widthIcon: 32,
          heightIcon: 32,
          width: 121,
          height: 112,
        },
      ]
    : []),
];

export const MenuItemCard = ({ isChekiExhibitable, identityId }: MenuItemCardProps) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleCreateItem = async (itemType: number, identityId: string) => {
    setIsLoading(true);
    const tempItemId = 'new' + Date.now();
    let eventType = 'fanme_listing_digital_bundle_click';
    let url = `/@${identityId}/item/${tempItemId}/create`;

    switch (itemType) {
      case ITEM_TYPE.DIGITAL_GACHA.value:
        eventType = 'fanme_listing_digital_gacha_click';
        url = `/@${identityId}/item/${tempItemId}/create?item_type=digitalGacha`;
        break;
      case ITEM_TYPE.CHEKI.value:
        eventType = 'fanme_listing_cheki_click';
        url = `/@${identityId}/item/${tempItemId}/create?item_type=cheki`;
        break;
    }

    sendGTMEvent({ event: eventType, shop_id: identityId });
    try {
      router.push(url);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {isLoading && <LoadingOverlay />}
      <div className="mx-auto h-screen max-w-120 space-y-4 p-4">
        {menuItems(isChekiExhibitable).map((item) => (
          <div
            key={item.id}
            className="relative flex cursor-pointer flex-row items-center justify-between rounded-md bg-white px-4 py-2"
            onClick={() => handleCreateItem(item.itemTypeValue ?? 0, identityId)}
            style={{ backgroundImage: `url(${item.src})`, backgroundSize: 'cover', backgroundPosition: 'center', }}
          >
            <div className="z-10 min-w-0 flex-[3.4] flex-col items-start p-[0.9vw] max-[400px]:pl-1">
              <div className="text-bold-16 underline decoration-yellow-100 decoration-10 underline-offset-[-4px]">
                {item.title}
              </div>
              <div className="mt-2 flex-nowrap text-medium-11">{item.description}</div>
              <ShopPublicImage
                src={item.srcIcon}
                width={item.widthIcon}
                height={item.heightIcon}
                alt={item.alt}
                className={clsx('mt-2', {
                  ['ml-9']: item.itemType === ITEM_TYPE.CHEKI.str,
                })}
              />
              <button
                onClick={() => handleCreateItem(item.itemTypeValue ?? 0, identityId)}
                className="my-2 h-8 w-28 rounded-2xl bg-black text-[12px] font-bold text-white"
              >
                出品する
              </button>
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

export default MenuItemCard;
