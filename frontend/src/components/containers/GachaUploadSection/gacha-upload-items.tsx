'use client';
import React, { useRef, useMemo, useState, useEffect, useCallback, RefObject } from 'react';
import { toast } from 'react-hot-toast';
import { closestCenter, DndContext, DragOverlay } from '@dnd-kit/core';
import { SortableContext, rectSortingStrategy } from '@dnd-kit/sortable';
import clsx from 'clsx';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import Button from '@/components/atoms/button';
import FloatButton from '@/components/atoms/button/float-button';
import Switch from '@/components/atoms/switch';
import CustomToast from '@/components/atoms/toast/custom-toast';
import SectionTitleWithNumber from '@/components/atoms/typography/section-title-with-number';
import UploadedItem from '@/components/containers/UploadedFile';
import ShopPublicImage from '@/components/ShopImage';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { useExhibitsStore } from '@/store/useExhibit';
import { useExhibitFormStore } from '@/store/useExhibitFormStore';
import { useInstructionModalStore } from '@/store/useInstructionModal';
import { SortableItem, getDragOverlayContent, useDraggableGacha } from './gacha-drag-drop';
import { ACCEPTED_FILE_TYPES } from '@/consts/file';
import { MAX_GACHA_ITEM_COUNT } from '@/consts/sizes';
import { useFloatingButton } from '@/hooks/useFloatingButton';
import { useGroupGachaItems } from '@/hooks/useGroupGachaItems';
import { useUploadProgress } from '@/hooks/useUploadProgress';
import { getUserIdentityId } from '@/utils/base';
import { awardLabels, validateDeleteWithGroupedItems } from '@/utils/gacha-validation-rules';
import { handleGachaFileUpload } from '@/utils/gachaUpload';
import { isLimitations } from '@/utils/limitation';
import { uploadThumbnail } from '@/utils/thumbnail';
import { ExhibitGachaItem } from '@/types/exhibitItem';
import { GachaItemFile, Award, AWARD_TYPE } from '@/types/gacha';

type GachaUploadItemsProps = {
  numbering: string;
  shopLimitation: ShopLimitation;
  totalGachaCountSectionRef: RefObject<HTMLDivElement>;
  isEdit?: boolean;
};

const GachaUploadItems = ({ numbering, shopLimitation, totalGachaCountSectionRef, isEdit }: GachaUploadItemsProps) => {
  const params = useParams();
  const identityId = getUserIdentityId(params.identityId as string);
  const itemId = params.id as string;
  const useExhibits = useExhibitsStore();
  const { exhibits, setItemFiles, setTotalCapacity } = useExhibits;
  const { isSorting, setIsSorting } = useExhibitFormStore();
  const { uploadProgress, handleProgress, resetProgress } = useUploadProgress();
  const { openInstructionModal } = useInstructionModalStore();
  const [isLoading, setIsLoading] = useState(false);
  const { isFileQuantityDefault } = isLimitations(shopLimitation);

  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]) as ExhibitGachaItem;
  const tempItemFiles = useMemo(() => exhibitItem?.itemFiles || [], [exhibitItem]);

  // ソート済みアイテムを管理するためのステート
  const [sortableItems, setSortableItems] = useState<GachaItemFile[]>([]);

  // tempItemFilesが変更されたときにソート可能なアイテムを初期化
  useEffect(() => {
    if (!isSorting) {
      setSortableItems(tempItemFiles);
    }
  }, [tempItemFiles, isSorting]);

  // 読み込み状態のファイルを監視
  useEffect(() => {
    const hasLoadingFiles = tempItemFiles.some((item) => item.isLoading);
    setIsLoading(hasLoadingFiles);
  }, [tempItemFiles]);

  // アイテムを賞のタイプ（S、A、B、C）でグループ化
  const groupedItems = useGroupGachaItems(isSorting ? sortableItems : tempItemFiles);

  // フックからドラッグ＆ドロップ機能を取得
  const { activeId, handleDragStart, handleDragEnd, handleComplete, sensors } = useDraggableGacha({
    items: sortableItems,
    onDragComplete: (updatedItems) => {
      setSortableItems(updatedItems);
    },
  });

  // ソートボタンクリック処理
  const handleSortStart = () => {
    setIsSorting(true);
  };

  // ソート完了処理
  const handleSortComplete = () => {
    setIsSorting(false);
    handleComplete();
    setItemFiles(itemId, sortableItems);
  };

  // すべての賞タイプに対する単一のアップロードref
  const uploadRef = useRef<HTMLInputElement>(null);

  const handleOpenIntroductionModal = () => {
    // 適切なコンテンツでインストラクションモーダルを開く
    const popupHeader = '中身を見せない「シークレット」設定';
    const popupContent = (
      <div className="flex flex-col items-center justify-center bg-gray-100 py-4">
        <ShopPublicImage src="/images/gacha/secrethint.webp" width={258} height={173} alt="upload instruction" />
      </div>
    );
    const popupFooter = (
      <div>
        <p>S賞のみ、「シークレット設定」を行うと、景品画像を隠した状態で出品できます。</p>
        <p>シークレットの設定は、「SECRET」アイコンをタップして切り替えます。</p>
      </div>
    );

    openInstructionModal(popupHeader, popupContent, popupFooter);
  };

  const handleOpenIntroductionUploadModal = () => {
    // 適切なコンテンツでインストラクションモーダルを開く
    const popupHeader = 'アップロード可能なファイル形式と容量';
    const popupContent = (
      <div className="flex flex-col items-center justify-center bg-gray-100 py-4">
        <ShopPublicImage
          src="/images/gacha/popupUploadLimitation.svg"
          width={308}
          height={116}
          alt="upload instruction"
        />
      </div>
    );
    const popupFooter = (
      <div>
        <p>アップロードできるファイルは合計で30点です。</p>
        <p>ただし、各ファイルにはアップロードできる上限が決まっています。</p>
      </div>
    );

    openInstructionModal(popupHeader, popupContent, popupFooter);
  };

  // アップロード用の現在の賞タイプを保存
  const [currentAwardType, setCurrentAwardType] = useState<Award>(4); // デフォルトはS賞

  // FloatButtonのrefを作成
  const floatButtonRef = useRef<HTMLDivElement>(null);
  // フックを使用してスクロール状態を取得（停止ポイントに親から渡されたrefを使用）
  const { isScrolled, isVisible } = useFloatingButton(
    floatButtonRef,
    tempItemFiles.length > 0,
    totalGachaCountSectionRef,
  );

  // 不要な再レンダリングを防ぐためにシークレットトグルハンドラをメモ化
  const handleToggleSecret = useCallback(
    (fileId: string, isSecret: boolean) => {
      const updatedFiles = tempItemFiles.map((file) => {
        // ファイルIDが一致する場合、S賞（awardType=4）の場合のみisSecretを設定、それ以外はfalse
        if (file.id === fileId) {
          return {
            ...file,
            isSecret: file.awardType === AWARD_TYPE.S ? isSecret : false,
          };
        }
        return file;
      });
      setItemFiles(itemId, updatedFiles);
    },
    [tempItemFiles, setItemFiles, itemId],
  );

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    // カスタムガチャアップロード機能を使用
    try {
      await handleGachaFileUpload({
        currentFiles: event.target.files,
        existingFiles: tempItemFiles,
        shopLimitation,
        identityId,
        setFiles: (files) => calculateTotalFileSize(files),
        onProgress: handleProgress,
        resetProgress,
        awardType: currentAwardType,
        itemId,
      });
    } catch (error) {
      console.error('Error processing media files:', error);
      toast.custom((t) => CustomToast(t, 'error', 'ファイルのアップロードに失敗しました'));
    }
  };

  const calculateTotalFileSize = useCallback(
    (files: GachaItemFile[]) => {
      const totalSize = files.reduce((total, file) => total + (file.size || 0), 0);
      setItemFiles(itemId, files);
      setTotalCapacity(itemId, totalSize);
    },
    [setItemFiles, itemId, setTotalCapacity],
  );

  // 編集モードの場合、tempItemFilesが変更されたときに自動的にcalculateTotalFileSizeを実行
  useEffect(() => {
    if (isEdit && tempItemFiles.length > 0) {
      const currentTotalSize = tempItemFiles.reduce((total, file) => total + (file.size || 0), 0);
      // 既存の合計容量と異なる場合のみ更新（無限ループを防ぐ）
      if (exhibitItem?.totalCapacity !== currentTotalSize) {
        calculateTotalFileSize(tempItemFiles);
      }
    }
  }, [isEdit, tempItemFiles, calculateTotalFileSize, exhibitItem?.totalCapacity]);

  // 不要な再レンダリングを防ぐためにファイル削除ハンドラをメモ化
  const handleDeleteFile = useCallback(
    (fileId: string) => {
      // グループ化されたアイテムを使用して削除検証を実行
      const validationResult = validateDeleteWithGroupedItems(groupedItems, fileId);
      if (!validationResult.valid) {
        toast.custom((t) => CustomToast(t, 'error', validationResult.message));
        return;
      }
      const updatedFiles = tempItemFiles.filter((file) => file.id !== fileId);
      calculateTotalFileSize(updatedFiles);
    },
    [tempItemFiles, groupedItems, calculateTotalFileSize],
  );

  // UploadedItemsListに基づいてsetThumbImage関数を実装
  const setItemThumbnail = useCallback(
    (fileId: string) => {
      return async (image: string) => {
        try {
          // 現在のファイル内でファイルを検索
          const fileIndex = tempItemFiles.findIndex((item) => item.id === fileId);
          if (fileIndex === -1) return;

          // 更新されたサムネイルで新しい配列を作成
          const updatedFiles = tempItemFiles.map((item) => {
            if (item.id === fileId) {
              return {
                ...item,
                thumbnail: item.type === 'audio' ? '/shop/images/voice.png' : image,
              };
            }
            return item;
          });

          // サムネイルをS3にアップロード
          const thumbnailUrl = await uploadThumbnail({
            img: image,
            fileId: fileId,
          });

          // アップロードされたサムネイルURLでファイルを更新
          const finalUpdatedFiles = updatedFiles.map((item) => {
            if (item.id === fileId) {
              return {
                ...item,
                thumbnail: thumbnailUrl.url,
              };
            }
            return item;
          });

          // ストア内のファイルを更新
          setItemFiles(itemId, finalUpdatedFiles);
        } catch (error) {
          console.error('Error updating thumbnail:', error);
          toast.custom((t) => CustomToast(t, 'error', 'サムネイルの更新に失敗しました'));
        }
      };
    },
    [tempItemFiles, setItemFiles, itemId],
  );

  const handleClickUpload = (awardType: Award = 4) => {
    // アップロード用の現在の賞タイプを設定
    setCurrentAwardType(awardType);

    // ファイル入力クリックをトリガー
    uploadRef.current?.setAttribute('accept', ACCEPTED_FILE_TYPES);
    uploadRef.current?.click();
  };

  // パフォーマンス向上のためにファイルグリッドのレンダリングをメモ化
  const renderFileGrid = useMemo(() => {
    // 名前付き関数を使用してdisplay name問題を解決
    function FileGrid(files: GachaItemFile[], awardType: Award) {
      if (files.length === 0) return null;

      return (
        <div className="grid grid-cols-3 gap-4">
          {files.map((file) => {
            const src =
              file.type === 'audio'
                ? '/shop/images/voice.png'
                : file.type === 'video'
                  ? file.thumbnail
                  : file.src || file.thumbnail;

            const fileContent = (
              <div className={`relative ${isSorting ? 'animate-shake_weekly' : ''}`}>
                <div className="mb-3">
                  <UploadedItem
                    id={file.id}
                    src={src || ''}
                    title={file.title || ''}
                    type={file.type}
                    setThumbImage={setItemThumbnail(file.id)}
                    thumbnail={file.preSignedThumbnailUrl || file.thumbnail}
                    handleDelete={!isSorting && !isEdit ? () => handleDeleteFile(file.id) : undefined}
                    showType={true}
                    progress={uploadProgress[file.id] || 0}
                    isLoading={file.isLoading}
                    sorting={isSorting}
                  />
                </div>

                {/* UploadedItemの下にSECRETトグルを追加 - S賞（awardType=4）の場合のみ表示 */}
                {!isSorting && awardType === AWARD_TYPE.S && (
                  <div className="flex items-center justify-center">
                    <span className="mr-2 text-xs">SECRET</span>
                    <Switch
                      id={`switch-${file.id}`}
                      isOn={!!file.isSecret}
                      handleOnChange={(e) => handleToggleSecret(file.id, e.target.checked)}
                      type="green"
                    />
                  </div>
                )}
              </div>
            );

            if (isSorting && !isEdit) {
              return (
                <SortableItem key={file.id} id={file.id}>
                  {fileContent}
                </SortableItem>
              );
            } else {
              return <div key={file.id}>{fileContent}</div>;
            }
          })}
        </div>
      );
    }
    return FileGrid;
  }, [isSorting, isEdit, handleDeleteFile, handleToggleSecret, setItemThumbnail, uploadProgress]);

  // 賞のセクションをレンダリング
  const renderAwardSection = (awardType: Award, files: GachaItemFile[]) => {
    const { label, icon } = awardLabels[awardType];
    const hasFiles = files.length > 0;

    // この賞タイプのファイルのみをフィルタリング
    const awardTypeFiles = isSorting ? sortableItems.filter((file) => file.awardType === awardType) : files;

    // 賞のセクションコンテンツを作成
    const awardSectionContent = (
      <div
        className={clsx(awardTypeFiles.length > 0 && 'mt-6 border-b border-gray-200 pb-3.75')}
        data-award-type={awardType}
      >
        {!isSorting && (
          <div>
            <div
              className={clsx(
                'my-4 flex items-center',
                awardTypeFiles.length === 0 && 'border-b border-gray-200 pb-3.75',
              )}
            >
              <ShopPublicImage src={icon} width={80} height={48} alt={label} />
              {!isEdit && (
                <div className="ml-auto flex gap-2">
                  <FloatButton buttonSize="md" onClick={() => handleClickUpload(awardType)} disabled={isLoading}>
                    <div className="flex w-full items-center justify-start pl-2">
                      <div className="mr-1 flex size-4.5 items-center justify-center rounded-full bg-secondary">
                        <ShopPublicImage
                          src="/images/icons/Close.svg"
                          width={8}
                          height={8}
                          alt="add"
                          className="rotate-45"
                        />
                      </div>
                      <span>アップロード</span>
                    </div>
                  </FloatButton>
                </div>
              )}
            </div>

            {/* S賞には常にSECRETメッセージを表示 */}
            {awardType === AWARD_TYPE.S && (
              <div className="mb-4 text-center text-medium-13 text-orange-100 underline">
                <span onClick={handleOpenIntroductionModal} className="cursor-pointer">
                  SECRET設定をすると、サムネイルを
                  <br />
                  「シークレット」状態で出品できます
                </span>
              </div>
            )}
          </div>
        )}

        {isSorting ? (
          <SortableContext
            items={[
              ...awardTypeFiles.map((file) => file.id),
              // ドロップ可能エリアIDをアイテムリストに追加
              `droppable-${awardType}`,
            ]}
            strategy={rectSortingStrategy}
          >
            <div className="mb-4 flex items-center">
              <ShopPublicImage src={icon} width={80} height={48} alt={label} />
            </div>
            {awardTypeFiles.length > 0 ? (
              renderFileGrid(awardTypeFiles, awardType)
            ) : (
              // ソート中の空のセクション用にドロップ可能エリアを作成
              <SortableItem id={`droppable-${awardType}`}>
                <div
                  className="flex min-h-[100px] items-center justify-center rounded-md border-2 border-dashed border-gray-200"
                  data-award-type={awardType}
                >
                  <p className="text-gray-400">ここにドラッグして{awardLabels[awardType].label}に移動</p>
                </div>
              </SortableItem>
            )}
          </SortableContext>
        ) : (
          hasFiles && renderFileGrid(files, awardType)
        )}
      </div>
    );

    // コンテンツを直接返す、DndContextはrenderContentにある
    return awardSectionContent;
  };

  // ファイル数に基づいて初期アップロード状態または賞のセクションを表示
  const renderContent = () => {
    const hasSFiles = groupedItems[0].length > 0; // S award (index 0 = award type 4)
    const hasAFiles = groupedItems[1].length > 0; // A award (index 1 = award type 3)
    const hasBFiles = groupedItems[2].length > 0; // B award (index 2 = award type 2)
    const hasCFiles = groupedItems[3].length > 0; // C award (index 3 = award type 1)

    if (!hasSFiles && !isEdit) {
      // 初期状態 - まだファイルがアップロードされていない
      return (
        <>
          <div className="mt-6 flex items-center justify-center">
            <Button buttonType="light-shadow" onClick={() => handleClickUpload()} disabled={isLoading}>
              <div className="absolute left-1 flex size-9.5 items-center justify-center rounded-full bg-gray-50">
                <ShopPublicImage src="/images/gacha/icons/uploadGacha.svg" width={22} height={22} alt="upload" />
              </div>
              <span>アップロード</span>
            </Button>
          </div>

          <div className="relative mb-4 flex flex-col items-center justify-center">
            <div className="triangle-down mb-1"></div>
            <ShopPublicImage src="/images/gacha/uploadhint.webp" width={344} height={120} alt="upload instruction" />
          </div>
        </>
      );
    }

    // ファイルがアップロードされた、賞のセクションを表示
    const content = (
      <div>
        {/* S賞セクション - ファイルがアップロードされると常に表示 */}
        {renderAwardSection(AWARD_TYPE.S, groupedItems[0])}
        {/* A賞セクション - 新規の時、Sにファイルがある場合に表示、編集の時、Aにファイルがある場合に表示 */}
        {(isEdit ? hasAFiles : hasSFiles) && renderAwardSection(AWARD_TYPE.A, groupedItems[1])}
        {/* B賞セクション - 新規の時、Aにファイルがある場合に表示、編集の時、Bにファイルがある場合に表示 */}
        {(isEdit ? hasBFiles : hasAFiles) && renderAwardSection(AWARD_TYPE.B, groupedItems[2])}
        {/* C賞セクション - 新規の時、Bにファイルがある場合に表示、編集の時、Cにファイルがある場合に表示 */}
        {(isEdit ? hasCFiles : hasBFiles) && renderAwardSection(AWARD_TYPE.C, groupedItems[3])}
      </div>
    );

    // ソートモード時はDndContextでラップ(編集時はソート禁止)
    if (isSorting) {
      return (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
          // パフォーマンス最適化を追加
          autoScroll={true}
        >
          {content}
          <DragOverlay zIndex={1000}>{activeId ? getDragOverlayContent(activeId, sortableItems) : null}</DragOverlay>
        </DndContext>
      );
    } else {
      return content;
    }
  };

  // コンポーネントに適切にdisplay nameを追加
  return (
    <section className="pt-4">
      <div className="px-4">
        <div className="mb-3 flex items-center justify-between">
          <SectionTitleWithNumber title="商品データをアップロード" numbering={numbering} required className="!mb-0" />
          <Button
            buttonType="light-small"
            buttonShape="circle"
            buttonSize="xxs"
            onClick={handleOpenIntroductionUploadModal}
          >
            <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
          </Button>
        </div>
        <p className="mb-5 text-medium-13">利用する賞にデータをアップロードしてください。</p>
        <div className="w-full text-center">
          <Link
            href="https://media.fanme.link/2453/"
            className="cursor-pointer text-medium-13 text-orange-100 underline"
            target="_blank"
          >
            ガチャのオススメ設定はコチラ
          </Link>
        </div>
        {tempItemFiles.length > 0 && !isEdit && (
          <div className="relative my-4 flex h-10 items-center justify-between">
            <div
              ref={floatButtonRef}
              className={clsx(
                'inset-x-0 m-auto flex items-center justify-center',
                !isScrolled && 'absolute',
                isScrolled && 'fixed top-15 z-50',
                isScrolled && isVisible ? 'slide-in-animation visible' : '',
              )}
            >
              <FloatButton
                buttonSize="sm"
                disabled={isLoading}
                onClick={isSorting ? handleSortComplete : handleSortStart}
              >
                {!isSorting && (
                  <div className="flex w-full items-center justify-start pl-2.5">
                    <ShopPublicImage src="/images/icons/Sort2.svg" width={16} height={16} alt="sort" />
                    <span className="ml-1">並び替え</span>
                  </div>
                )}
                {isSorting && (
                  <div className="flex w-full items-center justify-start">
                    <ShopPublicImage
                      src="/images/gacha/icons/Finish.svg"
                      width={32}
                      height={32}
                      alt="complete"
                      className="brightness-0"
                    />
                    <span>操作完了</span>
                  </div>
                )}
              </FloatButton>
            </div>
            <div className="absolute right-0 flex items-center">
              <ShopPublicImage
                src="/images/icons/GrayBox.svg"
                width={14}
                height={14}
                alt="upload"
                className="brightness-130"
              />
              <span className="text-regular-14 text-gray-400">
                <em className="text-regular-16 not-italic">{tempItemFiles.length}</em>
                {isFileQuantityDefault && <>/{MAX_GACHA_ITEM_COUNT}</>}
              </span>
            </div>
          </div>
        )}

        {renderContent()}

        {/* すべての賞タイプに対する非表示の単一ファイル入力 */}
        <input
          type="file"
          className="hidden"
          ref={uploadRef}
          onChange={(e) => {
            handleFileChange(e);
            e.target.value = ''; // 同じファイルを再度選択できるように入力をクリア
          }}
          multiple
          accept={ACCEPTED_FILE_TYPES}
        />
      </div>
    </section>
  );
};

export default GachaUploadItems;
