import React from 'react';
import ShopPublicImage from '@/components/ShopImage';

type WarningMessageProps = {
  message: string;
};

const WarningMessage: React.FC<WarningMessageProps> = ({ message }) => {
  return (
    <div className={`flex items-center`}>
      <div className="flex h-3 w-3 items-center justify-center rounded-full bg-red-500">
        <ShopPublicImage src="/images/icons/Exclamation.svg" alt="" aria-hidden="true" width={8} height={8} />
      </div>
      <span className="ml-1 text-medium-13 text-red-500">{message}</span>
    </div>
  );
};

export default WarningMessage;
