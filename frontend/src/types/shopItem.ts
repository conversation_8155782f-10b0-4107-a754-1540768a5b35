export type MediaType = 'image' | 'video' | 'audio';
import { AwardProbability } from '@/types/gacha';

export type ExhibitType = 'digitalBundle' | 'digitalGacha' | 'cheki' | 'printGacha';

// こちらは今後使わず、frontend/src/types/item.tsのITEM_TYPEを使用する
export const ITEM_TYPE = {
  digitalBundle: 0,
  digitalGacha: 1,
  cheki: 2,
  printGacha: 3,
} as const;
export type ItemType = (typeof ITEM_TYPE)[keyof typeof ITEM_TYPE];

export type ShopItemType = {
  id: number;
  title: string;
  available?: boolean;
  isNew: boolean;
  price: number;
  currentPrice: number;
  minPrice: number;
  thumbnail: string;
  fileType?: string;
  itemType: number;
  isAdded?: boolean;
  isCompleted?: boolean;
  collectedUniqueItemsCount?: number;
  isPurchased: boolean;
  isCheckout: boolean;
  hasPassword: boolean;
  isLimited?: boolean;
  onSale?: OnSale;
  forSale?: ForSale;
  isForSale?: boolean;
  isSingleSales: boolean;
  hasBenefit: boolean;
  stockCount?: number;
  fileQuantities?: FileQuantity[];
};

// TODO startとendどっちかだけの場合も考慮する
export interface OnSale {
  startAt?: Date | string | null;
  endAt?: Date | string | null;
  discountRate?: number;
}

// TODO startとendどっちかだけの場合も考慮する
export interface ForSale {
  startAt?: string | null;
  endAt?: string | null;
}

export interface ItemOption {
  onSale?: OnSale;
  forSale?: ForSale;
  isSingleSales: boolean;
  qtyTotal: number;
  remainingAmount: number;
  password: string;
}

export interface Period {
  start?: string | Date;
  end?: string | Date;
}

export enum PeriodState {
  BeforeStart = 'beforeStart',
  Ongoing = 'ongoing',
  NearlyEnded = 'nearlyEnded',
  Ended = 'ended',
}

// Base item type that can be extended by both SingleItem and GachaItem
export interface BaseItemFile {
  id: string;
  title: string;
  src: string;
  thumbnail?: string;
  type?: MediaType;
  size?: number;
  duration?: number;
  thumbnailRatio?: number;
  preSignedThumbnailUrl?: string;
  isLoading?: boolean;
}

// Standard item type (previously SingleItem)
export interface SingleItem extends BaseItemFile {
  file?: File;
  fullThumbnail?: string;
  processedThumbnail?: string;
  blur?: number;
  watermark?: number;
  selected?: boolean;
  price?: number;
  currentPrice?: number;
  ratio?: number;
  extension?: string;
  isCheckout?: boolean;
  isInCart?: boolean;
  isSingleSale?: boolean;
  sortIndex?: number;
  isPurchased?: boolean;
}

export type ItemFiles = SingleItem[];

// Base benefit interface
export interface BaseBenefit {
  description?: string;
}

// Standard benefit
export interface Benefit extends BaseBenefit {
  benefitFiles: SingleItem[];
}

export type Discount = {
  percentage: number;
  start?: Date | string;
  end?: Date | string;
};

// Base shop item detail interface

export interface BaseShopItemDetail {
  item: {
    id: string;
    title: string;
    description: string;
    thumbnail: string;
    thumbnailRatio: number;
    price: number;
    currentPrice?: number;
    discountRate?: number;
    period?: Period;
    hasPassword?: boolean;
    discount?: Discount;
    available?: boolean;
    awardProbabilities?: AwardProbability[];
    isDuplicatedDigitalGachaItems?: boolean;
    itemType: number;
    tags?: string[];
    isPurchased?: boolean;
    isCheckout?: boolean;
    purchasedCount: number;
    limited?: number;
    limitedPerUser?: number;
    remainingAmount?: number;
    thumbnailType: 'custom' | 'upload';
    thumbnailBlurLevel: number;
    thumbnailWatermarkLevel: number;
  };
}

// Standard shop item detail
export interface ShopItemDetail extends BaseShopItemDetail {
  item: BaseShopItemDetail['item'] & {
    singleSale: boolean;
    itemFiles: SingleItem[];
    samples?: SingleItem[];
    benefits?: Benefit;
  };
}

export interface ChekiItemDetail extends BaseShopItemDetail {
  item: BaseShopItemDetail['item'] & {
    samples?: SingleItem[];
    benefits?: Benefit;
  };
}

export type FileQuantity = {
  fileType: string;
  quantity: number;
};

export enum OptionType {
  SINGLE_SALE = 'singleSale',
  LIMITED = 'limited',
  PERIOD = 'period',
  PASSWORD = 'password',
  DISCOUNT = 'discount',
  DUPLICATE = 'duplicate',
}
