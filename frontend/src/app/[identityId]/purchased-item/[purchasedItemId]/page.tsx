import React from 'react';
import { notFound } from 'next/navigation';
import Accordion from '@/components/atoms/accordion';
import CreatorInfo from '@/components/containers/creator-info';
import { getPurchasedItem } from '@/lib/server-api/purchased-item-endpoint/purchased-item-endpoint';
import { getShop } from '@/lib/server-api/shop-endpoint/shop-endpoint';
import PurchaseOptionSection from './purchase-option-section';
import MainItemSection from '@/app/[identityId]/purchased-item/[purchasedItemId]/main-item-section';
import PaymentReceipt from '@/app/[identityId]/purchased-item/[purchasedItemId]/payment-receipt';
import BenefitsSection from '@/app/[identityId]/viewer/[itemId]/BenefitsSection';
import NotesSection from '@/app/[identityId]/viewer/[itemId]/NotesSection';
import { getItem } from '@/app/actions/shopItem';
import { getUserIdentityId } from '@/utils/base';

type ItemViewerProps = {
  identityId: string;
  purchasedItemId: string;
};

const ItemPurchaseDetail = async ({ params }: { params: ItemViewerProps }) => {
  const identityId = getUserIdentityId(params.identityId);
  if (isNaN(Number(params.purchasedItemId))) {
    return notFound();
  }

  try {
    const purchasedItemResponse = await getPurchasedItem(parseInt(params.purchasedItemId));
    const purchasedItemDetail = purchasedItemResponse.data?.purchasedItem;
    if (!purchasedItemDetail || !purchasedItemDetail.itemId) throw new Error('purchasedItem not found');

    const itemResponse = await getItem(purchasedItemDetail.itemId.toString(), identityId, true);
    const itemData = await itemResponse.json();
    const item = itemData.data?.item;
    if (!item || !item.id) throw new Error('item not found');

    const shopResponse = await getShop(identityId);
    const shop = shopResponse.data?.shop;
    if (!shop) throw new Error('shop not found');

    return (
      <div className="flex flex-col items-center justify-center gap-4 bg-gray-100">
        <div className="w-full px-4 pt-4">
          <CreatorInfo
            creatorName={shop.creatorName}
            identityId={shop.creatorAccountIdentity}
            creatorIcon={shop.creatorIconUri}
          />
        </div>
        <MainItemSection name={item.name || item.title} thumbnail={item.thumbnail} description={item.description} />
        {item.benefits?.benefitFiles && item.benefits.benefitFiles.length > 0 && (
          <BenefitsSection itemId={item.id?.toString()} benefits={item.benefits} />
        )}
        <PurchaseOptionSection purchasedItemDetail={purchasedItemDetail} />
        <div className="w-full px-4">
          <Accordion title={'決済情報'} type="white">
            <div className="p-2">
              <PaymentReceipt purchasedItemDetail={purchasedItemDetail} />
            </div>
          </Accordion>
        </div>
        <NotesSection isDigital={false} />
      </div>
    );
  } catch (e) {
    console.error(JSON.stringify({ e }));
    return notFound();
  }
};

export default ItemPurchaseDetail;
