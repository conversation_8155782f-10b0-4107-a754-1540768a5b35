package jp.co.torihada.fanme.modules.payment.usecases.monthlysales

import com.github.michaelbull.result.unwrap
import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import java.time.Instant
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.modules.payment.Config
import jp.co.torihada.fanme.modules.payment.factories.MonthlySellerSaleFactory
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class GetMonthlySellerSalesTest {

    @Inject lateinit var commonConfig: CommonConfig

    @Inject lateinit var config: Config

    @Inject lateinit var getMonthlySellerSales: GetMonthlySellerSales

    @Test
    @TestTransaction
    fun `test get monthly sales for multiple seller IDs successfully`() {
        val sellerUserId1 = "sellerUserId1"
        val sellerUserId2 = "sellerUserId2"
        val yearMonth1 = "202301"
        val yearMonth2 = "202302"
        val expirationDate = Instant.now().plusSeconds(3600)

        val sale1 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId1,
                yearMonth = yearMonth1,
                transactionAmount = 2000,
                merged = true,
                expirationDate = expirationDate,
            )
        sale1.sellerSalesAmount = 1000
        sale1.persist()

        val sale2 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId1,
                yearMonth = yearMonth2,
                transactionAmount = 3000,
                merged = false,
                expirationDate = expirationDate,
            )
        sale2.sellerSalesAmount = 1500
        sale2.persist()

        val sale3 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId2,
                yearMonth = yearMonth1,
                transactionAmount = 1500,
                merged = true,
                expirationDate = expirationDate,
            )
        sale3.sellerSalesAmount = 750
        sale3.persist()

        val input =
            GetMonthlySellerSales.Input(
                sellerUserIds = listOf(sellerUserId1, sellerUserId2),
                fromYearMonth = "202301",
                toYearMonth = "202302",
            )

        val result = getMonthlySellerSales.execute(input).unwrap()

        assertEquals(3, result.monthlySalesList.size)

        val seller1Sales = result.monthlySalesList.filter { it.sellerUserId == sellerUserId1 }
        assertEquals(2, seller1Sales.size)

        val seller1YearMonth1 = seller1Sales.find { it.yearMonth == yearMonth1 }
        assertEquals(sellerUserId1, seller1YearMonth1?.sellerUserId)
        assertEquals(yearMonth1, seller1YearMonth1?.yearMonth)
        assertEquals(1000, seller1YearMonth1?.sellerSalesAmount)

        val seller1YearMonth2 = seller1Sales.find { it.yearMonth == yearMonth2 }
        assertEquals(sellerUserId1, seller1YearMonth2?.sellerUserId)
        assertEquals(yearMonth2, seller1YearMonth2?.yearMonth)
        assertEquals(1500, seller1YearMonth2?.sellerSalesAmount)

        val seller2Sales = result.monthlySalesList.filter { it.sellerUserId == sellerUserId2 }
        assertEquals(1, seller2Sales.size)

        val seller2YearMonth1 = seller2Sales.find { it.yearMonth == yearMonth1 }
        assertEquals(sellerUserId2, seller2YearMonth1?.sellerUserId)
        assertEquals(yearMonth1, seller2YearMonth1?.yearMonth)
        assertEquals(750, seller2YearMonth1?.sellerSalesAmount)
    }

    @Test
    @TestTransaction
    fun `test get monthly sales with date range filtering`() {
        val sellerUserId = "sellerUserId3"
        val yearMonth1 = "202301"
        val yearMonth2 = "202302"
        val yearMonth3 = "202303"
        val expirationDate = Instant.now().plusSeconds(3600)

        val sale1 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId,
                yearMonth = yearMonth1,
                merged = true,
                expirationDate = expirationDate,
            )
        sale1.sellerSalesAmount = 1000
        sale1.persist()

        val sale2 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId,
                yearMonth = yearMonth2,
                merged = true,
                expirationDate = expirationDate,
            )
        sale2.sellerSalesAmount = 1500
        sale2.persist()

        val sale3 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId,
                yearMonth = yearMonth3,
                merged = true,
                expirationDate = expirationDate,
            )
        sale3.sellerSalesAmount = 2000
        sale3.persist()

        val input =
            GetMonthlySellerSales.Input(
                sellerUserIds = listOf(sellerUserId),
                fromYearMonth = "202302",
                toYearMonth = "202302",
            )

        val result = getMonthlySellerSales.execute(input).unwrap()

        assertEquals(1, result.monthlySalesList.size)
        assertEquals(yearMonth2, result.monthlySalesList[0].yearMonth)
        assertEquals(1500, result.monthlySalesList[0].sellerSalesAmount)
    }

    @Test
    @TestTransaction
    fun `test get monthly sales with empty seller IDs list returns empty result`() {
        val input = GetMonthlySellerSales.Input(sellerUserIds = emptyList())
        val result = getMonthlySellerSales.execute(input).unwrap()

        assertEquals(0, result.monthlySalesList.size)
    }

    @Test
    @TestTransaction
    fun `test get monthly sales with non-existent seller IDs returns empty result for those IDs`() {
        val existingSellerUserId = "existingSellerUserId"
        val nonExistingSellerUserId = "nonExistingSellerUserId"
        val yearMonth = "202301"
        val expirationDate = Instant.now().plusSeconds(3600)

        val sale =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = existingSellerUserId,
                yearMonth = yearMonth,
                merged = true,
                expirationDate = expirationDate,
            )
        sale.sellerSalesAmount = 1000
        sale.persist()

        val input =
            GetMonthlySellerSales.Input(
                sellerUserIds = listOf(existingSellerUserId, nonExistingSellerUserId)
            )

        val result = getMonthlySellerSales.execute(input).unwrap()

        assertEquals(1, result.monthlySalesList.size)
        assertEquals(existingSellerUserId, result.monthlySalesList[0].sellerUserId)
        assertEquals(yearMonth, result.monthlySalesList[0].yearMonth)
        assertEquals(1000, result.monthlySalesList[0].sellerSalesAmount)
    }

    @Test
    @TestTransaction
    fun `test get monthly sales with default date range parameters`() {
        val sellerUserId = "sellerUserId4"
        val earlyYearMonth = "202001"
        val lateYearMonth = "203001"
        val expirationDate = Instant.now().plusSeconds(3600)

        val sale1 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId,
                yearMonth = earlyYearMonth,
                merged = true,
                expirationDate = expirationDate,
            )
        sale1.sellerSalesAmount = 1000
        sale1.persist()

        val sale2 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId,
                yearMonth = lateYearMonth,
                merged = true,
                expirationDate = expirationDate,
            )
        sale2.sellerSalesAmount = 2000
        sale2.persist()

        val input = GetMonthlySellerSales.Input(sellerUserIds = listOf(sellerUserId))

        val result = getMonthlySellerSales.execute(input).unwrap()

        assertEquals(2, result.monthlySalesList.size)
        assertTrue(result.monthlySalesList.any { it.yearMonth == earlyYearMonth })
        assertTrue(result.monthlySalesList.any { it.yearMonth == lateYearMonth })
    }

    @Test
    @TestTransaction
    fun `test get monthly sales with fromYearMonth only`() {
        val sellerUserId = "sellerUserId5"
        val yearMonth1 = "202301"
        val yearMonth2 = "202302"
        val yearMonth3 = "202303"
        val expirationDate = Instant.now().plusSeconds(3600)

        val sale1 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId,
                yearMonth = yearMonth1,
                merged = true,
                expirationDate = expirationDate,
            )
        sale1.sellerSalesAmount = 1000
        sale1.persist()

        val sale2 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId,
                yearMonth = yearMonth2,
                merged = true,
                expirationDate = expirationDate,
            )
        sale2.sellerSalesAmount = 1500
        sale2.persist()

        val sale3 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId,
                yearMonth = yearMonth3,
                merged = true,
                expirationDate = expirationDate,
            )
        sale3.sellerSalesAmount = 2000
        sale3.persist()

        val input =
            GetMonthlySellerSales.Input(
                sellerUserIds = listOf(sellerUserId),
                fromYearMonth = "202302",
                toYearMonth = null,
            )

        val result = getMonthlySellerSales.execute(input).unwrap()

        assertEquals(2, result.monthlySalesList.size)
        assertTrue(result.monthlySalesList.all { it.yearMonth >= "202302" })
        assertTrue(
            result.monthlySalesList.any {
                it.yearMonth == yearMonth2 && it.sellerSalesAmount == 1500
            }
        )
        assertTrue(
            result.monthlySalesList.any {
                it.yearMonth == yearMonth3 && it.sellerSalesAmount == 2000
            }
        )
    }

    @Test
    @TestTransaction
    fun `test get monthly sales with toYearMonth only`() {
        val sellerUserId = "sellerUserId6"
        val yearMonth1 = "202301"
        val yearMonth2 = "202302"
        val yearMonth3 = "202303"
        val expirationDate = Instant.now().plusSeconds(3600)

        val sale1 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId,
                yearMonth = yearMonth1,
                merged = true,
                expirationDate = expirationDate,
            )
        sale1.sellerSalesAmount = 1000
        sale1.persist()

        val sale2 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId,
                yearMonth = yearMonth2,
                merged = true,
                expirationDate = expirationDate,
            )
        sale2.sellerSalesAmount = 1500
        sale2.persist()

        val sale3 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId,
                yearMonth = yearMonth3,
                merged = true,
                expirationDate = expirationDate,
            )
        sale3.sellerSalesAmount = 2000
        sale3.persist()

        val input =
            GetMonthlySellerSales.Input(
                sellerUserIds = listOf(sellerUserId),
                fromYearMonth = null,
                toYearMonth = "202302",
            )

        val result = getMonthlySellerSales.execute(input).unwrap()

        assertEquals(2, result.monthlySalesList.size)
        assertTrue(result.monthlySalesList.all { it.yearMonth <= "202302" })
        assertTrue(
            result.monthlySalesList.any {
                it.yearMonth == yearMonth1 && it.sellerSalesAmount == 1000
            }
        )
        assertTrue(
            result.monthlySalesList.any {
                it.yearMonth == yearMonth2 && it.sellerSalesAmount == 1500
            }
        )
    }

    @Test
    @TestTransaction
    fun `test empty input returns empty result`() {
        val input = GetMonthlySellerSales.Input(sellerUserIds = emptyList())

        val result = getMonthlySellerSales.execute(input).unwrap()

        assertEquals(0, result.monthlySalesList.size)
    }
}
