package jp.co.torihada.fanme.endpoints.console.agency

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.models.Agency
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.notNullValue
import org.junit.jupiter.api.Test
import org.mockito.Mockito.times
import org.mockito.Mockito.`when`

@QuarkusTest
class AgencySuperRoleTest : AgencyEndpointTestBase() {

    @Test
    @TestSecurity(
        user = "super-user-1",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-user-1")],
    )
    fun `SUPERロールでエージェンシー一覧を取得できること（全てのエージェンシー）`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/agencies")
            .then()
            .statusCode(200)
            .body(notNullValue())
            .body("data.agencies.size()", equalTo(3))
    }

    @Test
    @TestSecurity(
        user = "super-user-1",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-user-1")],
    )
    fun `SUPERロールでエージェンシーの売上データを取得できること`() {
        val agency = Agency.findAll().firstResult()
        if (agency == null) {
            throw IllegalStateException("No agency found for test")
        }
        val agencyId = agency.id.toString()

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/agencies/$agencyId/sales")
            .then()
            .statusCode(200)
            .body(notNullValue())
            .body("data.agencySales.usersSales.size()", equalTo(3))
    }

    @Test
    @TestSecurity(
        user = "super-user-1",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-user-1")],
    )
    fun `存在しないエージェンシーIDの場合は404が返ること`() {
        val nonExistentAgencyId = "999999"

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/agencies/$nonExistentAgencyId/sales")
            .then()
            .statusCode(404)
    }
}
