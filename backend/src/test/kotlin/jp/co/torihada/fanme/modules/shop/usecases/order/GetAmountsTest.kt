// package jp.co.torihada.fanme.modules.shop.usecases.order
//
// import com.github.michaelbull.result.unwrap
// import io.quarkus.test.TestTransaction
// import io.quarkus.test.junit.QuarkusTest
// import jakarta.inject.Inject
// import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
// import jp.co.torihada.fanme.modules.fanme.models.User
// import jp.co.torihada.fanme.modules.shop.factories.ItemFactory
// import jp.co.torihada.fanme.modules.shop.factories.ShopFactory
// import jp.co.torihada.fanme.modules.shop.models.Cart
// import jp.co.torihada.fanme.modules.shop.models.CartItem
// import jp.co.torihada.fanme.modules.shop.models.ItemType
// import org.junit.jupiter.api.Assertions.assertEquals
// import org.junit.jupiter.api.BeforeEach
// import org.junit.jupiter.api.Test
//
// TODO GetAmountsのusecaseの中でfanmeのUserを参照しているので、テストが通らない
// @QuarkusTest
// class GetAmountsTest {
//
//    @Inject private lateinit var getAmounts: GetAmounts
//
//    @Test
//    @TestTransaction
//    fun `test get amounts with digital bundle`() {
//        val user =
//            UserFactory.createTestUser(
//                uid = "test-user-uid",
//                name = "Test User",
//                accountIdentity = "test-user-account-identity",
//            ) ?: throw IllegalStateException("Failed to create test user")
//        user.persistAndFlush()
//
//        val shop = ShopFactory.new()
//        shop.persistAndFlush()
//
//        val digitalBundleItem =
//            ItemFactory.new(
//                shopId = shop.id!!,
//                name = "デジタルバンドル",
//                itemType = ItemType.DIGITAL_BUNDLE,
//                price = 1000,
//            )
//        digitalBundleItem.persistAndFlush()
//
//        val cart = Cart.create(user.uid!!, shop.id!!)
//
//        val digitalBundleCartItem =
//            CartItem.create(
//                cartId = cart.id!!,
//                itemId = digitalBundleItem.id!!,
//                quantity = 1,
//                singleFileId = null,
//            )
//        digitalBundleCartItem.persistAndFlush()
//
//        cart.items.add(digitalBundleCartItem)
//        cart.persistAndFlush()
//
//        val input =
//            GetAmounts.Input(
//                cartId = cart.id!!,
//                cartItemIds = listOf(digitalBundleCartItem.id!!),
//                tip = 500,
//            )
//
//        val result = getAmounts.execute(input).unwrap()
//
//        assertEquals(1500, result.total)
//        assertEquals(1000, result.itemAmount)
//        assertEquals(500, result.tip)
//        assertEquals(1160, result.profit)
//        assertEquals(340, result.fee)
//        assertEquals(null, result.deliveryFee)
//    }
//
//    @Test
//    @TestTransaction
//    fun `test get amounts with cheki`() {
//        val user =
//            UserFactory.createTestUser(
//                uid = "test-user-uid",
//                name = "Test User",
//                accountIdentity = "test-user-account-identity",
//            ) ?: throw IllegalStateException("Failed to create test user")
//        user.persistAndFlush()
//
//        val shop = ShopFactory.new()
//        shop.persistAndFlush()
//
//        val chekiItem =
//            ItemFactory.new(
//                shopId = shop.id!!,
//                name = "チェキ",
//                itemType = ItemType.CHEKI,
//                price = 1500,
//            )
//        chekiItem.persistAndFlush()
//
//        val cart = Cart.create(user.uid!!, shop.id!!)
//
//        val chekiCartItem =
//            CartItem.create(
//                cartId = cart.id!!,
//                itemId = chekiItem.id!!,
//                quantity = 2,
//                singleFileId = null,
//            )
//        chekiCartItem.persistAndFlush()
//
//        cart.items.add(chekiCartItem)
//        cart.persistAndFlush()
//
//        val input =
//            GetAmounts.Input(
//                cartId = cart.id!!,
//                cartItemIds = listOf(chekiCartItem.id!!),
//                tip = 500,
//            )
//
//        val result = getAmounts.execute(input).unwrap()
//
//        assertEquals(3900, result.total)
//        assertEquals(3000, result.itemAmount)
//        assertEquals(500, result.tip)
//        assertEquals(2380, result.profit)
//        assertEquals(1520, result.fee)
//        assertEquals(400, result.deliveryFee)
//    }
//
//    @Test
//    @TestTransaction
//    fun `test get amounts with acrylic stand`() {
//        val user =
//            UserFactory.createTestUser(
//                uid = "test-user-uid",
//                name = "Test User",
//                accountIdentity = "test-user-account-identity",
//            ) ?: throw IllegalStateException("Failed to create test user")
//        user.persistAndFlush()
//
//        val shop = ShopFactory.new()
//        shop.persistAndFlush()
//
//        val acrylicStandItem =
//            ItemFactory.new(
//                shopId = shop.id!!,
//                name = "【アクスタ枠付】アクリルスタンド",
//                itemType = ItemType.CHEKI,
//                price = 2000,
//            )
//        acrylicStandItem.persistAndFlush()
//
//        val cart = Cart.create(user.uid!!, shop.id!!)
//
//        val acrylicStandCartItem =
//            CartItem.create(
//                cartId = cart.id!!,
//                itemId = acrylicStandItem.id!!,
//                quantity = 1,
//                singleFileId = null,
//            )
//        acrylicStandCartItem.persistAndFlush()
//
//        cart.items.add(acrylicStandCartItem)
//        cart.persistAndFlush()
//
//        val input =
//            GetAmounts.Input(
//                cartId = cart.id!!,
//                cartItemIds = listOf(acrylicStandCartItem.id!!),
//                tip = 500,
//            )
//
//        val result = getAmounts.execute(input).unwrap()
//
//        assertEquals(2900, result.total)
//        assertEquals(2000, result.itemAmount)
//        assertEquals(500, result.tip)
//        assertEquals(1374, result.profit)
//        assertEquals(1526, result.fee)
//        assertEquals(400, result.deliveryFee)
//    }
// }
