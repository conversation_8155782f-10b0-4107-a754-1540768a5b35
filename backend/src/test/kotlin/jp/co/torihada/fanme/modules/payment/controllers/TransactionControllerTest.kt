package jp.co.torihada.fanme.modules.payment.controllers

import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import java.time.Instant
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.factories.TransactionFactory
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class TransactionControllerTest {

    @Inject lateinit var transactionController: TransactionController

    @Test
    @TestTransaction
    fun `test getSellerTransactions returns empty list when no data exists`() {
        val sellerUserIds = listOf("testSeller1", "testSeller2")
        val yearMonth = "202401"

        val result = transactionController.getSellerTransactions(sellerUserIds, yearMonth)

        assertNotNull(result)
        assertEquals(0, result.size)
    }

    @Test
    @TestTransaction
    fun `test getSellerTransactions with empty seller list returns empty list`() {
        val sellerUserIds = emptyList<String>()
        val yearMonth = "202401"

        val result = transactionController.getSellerTransactions(sellerUserIds, yearMonth)

        assertNotNull(result)
        assertEquals(0, result.size)
    }

    @Test
    @TestTransaction
    fun `test getSellerTransactions returns correct transactions for single seller`() {
        val sellerUserId = "testSeller1"
        val yearMonth = "202401"

        val transaction1 =
            TransactionFactory.new(
                    purchaserUserId = "buyer1",
                    sellerUserId = sellerUserId,
                    orderedAt = Instant.parse("2024-01-15T10:00:00Z"),
                    amount = 1000,
                    totalAmount = 1000,
                    status = Const.TransactionStatus.Success.value,
                )
                .apply {
                    createdAt = Instant.parse("2024-01-15T10:00:00Z")
                    persist()
                }

        val transaction2 =
            TransactionFactory.new(
                    purchaserUserId = "buyer2",
                    sellerUserId = sellerUserId,
                    orderedAt = Instant.parse("2024-01-20T10:00:00Z"),
                    amount = 2000,
                    totalAmount = 2000,
                    status = Const.TransactionStatus.Success.value,
                )
                .apply {
                    createdAt = Instant.parse("2024-01-20T10:00:00Z")
                    persist()
                }

        val result = transactionController.getSellerTransactions(listOf(sellerUserId), yearMonth)

        assertNotNull(result)
        assertEquals(2, result.size)
        assertEquals(setOf("buyer1", "buyer2"), result.map { it.purchaserUserId }.toSet())
    }

    @Test
    @TestTransaction
    fun `test getSellerTransactions filters out failed transactions`() {
        val sellerUserId = "testSeller1"
        val yearMonth = "202401"

        TransactionFactory.new(
                purchaserUserId = "buyer1",
                sellerUserId = sellerUserId,
                orderedAt = Instant.parse("2024-01-15T10:00:00Z"),
                amount = 1000,
                totalAmount = 1000,
                status = Const.TransactionStatus.Success.value,
            )
            .apply {
                createdAt = Instant.parse("2024-01-15T10:00:00Z")
                persist()
            }

        TransactionFactory.new(
                purchaserUserId = "buyer2",
                sellerUserId = sellerUserId,
                orderedAt = Instant.parse("2024-01-20T10:00:00Z"),
                amount = 2000,
                totalAmount = 2000,
                status = Const.TransactionStatus.Failure.value,
            )
            .apply {
                createdAt = Instant.parse("2024-01-20T10:00:00Z")
                persist()
            }

        val result = transactionController.getSellerTransactions(listOf(sellerUserId), yearMonth)

        assertNotNull(result)
        assertEquals(1, result.size)
        assertEquals("buyer1", result[0].purchaserUserId)
    }

    @Test
    @TestTransaction
    fun `test getSellerTransactions filters by month correctly`() {
        val sellerUserId = "testSeller1"
        val targetYearMonth = "202401"

        TransactionFactory.new(
                purchaserUserId = "buyer1",
                sellerUserId = sellerUserId,
                orderedAt = Instant.parse("2024-01-15T10:00:00Z"),
                amount = 1000,
                totalAmount = 1000,
                status = Const.TransactionStatus.Success.value,
            )
            .apply {
                createdAt = Instant.parse("2024-01-15T10:00:00Z")
                persist()
            }

        TransactionFactory.new(
                purchaserUserId = "buyer2",
                sellerUserId = sellerUserId,
                orderedAt = Instant.parse("2024-02-15T10:00:00Z"),
                amount = 2000,
                totalAmount = 2000,
                status = Const.TransactionStatus.Success.value,
            )
            .apply {
                createdAt = Instant.parse("2024-02-15T10:00:00Z")
                persist()
            }

        val result =
            transactionController.getSellerTransactions(listOf(sellerUserId), targetYearMonth)

        assertNotNull(result)
        assertEquals(1, result.size)
        assertEquals("buyer1", result[0].purchaserUserId)
    }
}
