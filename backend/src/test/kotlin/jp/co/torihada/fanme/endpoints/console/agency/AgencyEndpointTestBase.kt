package jp.co.torihada.fanme.endpoints.console.agency

import io.quarkus.test.InjectMock
import io.quarkus.test.junit.QuarkusTest
import jakarta.transaction.Transactional
import java.time.Instant
import java.time.YearMonth
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.payment.controllers.MonthlySellerSalesController
import jp.co.torihada.fanme.modules.payment.controllers.TransactionController
import jp.co.torihada.fanme.modules.payment.factories.TransactionFactory
import jp.co.torihada.fanme.modules.payment.mock.CustomArgumentMatchers.any
import jp.co.torihada.fanme.modules.payment.models.MonthlySellerSale
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.payment.usecases.monthlysales.GetMonthlySellerSales
import jp.co.torihada.fanme.modules.payment.utils.YearMonth as YearMonthUtil
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.TestInstance
import org.mockito.Mockito.`when`

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
abstract class AgencyEndpointTestBase {

    @InjectMock lateinit var monthlySellerSalesController: MonthlySellerSalesController
    @InjectMock lateinit var transactionController: TransactionController

    @BeforeEach
    @Transactional
    fun setup() {
        ConsoleUser.deleteAll()
        Agency.deleteAll()

        val agency =
            Agency().apply {
                name = "Test Agency"
                createdAt = Instant.now()
            }
        agency.persist()

        val unauthorizedAgency =
            Agency().apply {
                name = "Unauthorized Agency"
                createdAt = Instant.now()
            }
        unauthorizedAgency.persist()

        val testAgentAgency =
            Agency().apply {
                name = "Test Agent Agency"
                createdAt = Instant.now()
            }
        testAgentAgency.persist()

        val superUser =
            ConsoleUserFactory.createTestUser(
                "super-user-1",
                "Super User",
                "<EMAIL>",
            )
        val bizUser =
            ConsoleUserFactory.createTestUser("biz-user-1", "Biz User", "<EMAIL>")
        val agentUser =
            ConsoleUserFactory.createTestUser(
                "agent-user-1",
                "Agent User",
                "<EMAIL>",
            )
        val testAgentUser =
            ConsoleUserFactory.createTestUser(
                "test-agent",
                "Test Agent User",
                "<EMAIL>",
            )
        val creatorUser1 =
            ConsoleUserFactory.createTestUser(
                "creator-user-1",
                "Creator User 1",
                "<EMAIL>",
            )
        val creatorUser2 =
            ConsoleUserFactory.createTestUser(
                "creator-user-2",
                "Creator User 2",
                "<EMAIL>",
            )
        val testAgentCreatorUser =
            ConsoleUserFactory.createTestUser(
                "test-agent-creator",
                "Test Agent Creator",
                "<EMAIL>",
            )

        val users =
            listOf(
                    superUser?.id?.let { ConsoleUserFactory.new(it, null, UserRole.SUPER_VALUE) },
                    bizUser?.id?.let { ConsoleUserFactory.new(it, null, UserRole.BIZ_VALUE) },
                    agentUser?.id?.let {
                        ConsoleUserFactory.new(it, agency.id, UserRole.AGENT_VALUE)
                    },
                    testAgentUser?.id?.let {
                        ConsoleUserFactory.new(it, testAgentAgency.id, UserRole.AGENT_VALUE)
                    },
                    creatorUser1?.id?.let {
                        ConsoleUserFactory.new(it, agency.id, UserRole.CREATOR_VALUE)
                    },
                    creatorUser2?.id?.let {
                        ConsoleUserFactory.new(it, agency.id, UserRole.CREATOR_VALUE)
                    },
                    testAgentCreatorUser?.id?.let {
                        ConsoleUserFactory.new(it, testAgentAgency.id, UserRole.CREATOR_VALUE)
                    },
                )
                .filterNotNull()

        users.forEach { it.persistAndFlush() }

        setupMockBalances()
    }

    protected fun setupMockBalances() {
        val currentYearMonth = YearMonth.now()
        val lastYearMonth = currentYearMonth.minusMonths(1)
        val thisMonthStr = currentYearMonth.toString().replace("-", "")
        val lastMonthStr = lastYearMonth.toString().replace("-", "")

        `when`(monthlySellerSalesController.getMonthlySales(any(), any(), any()))
            .thenReturn(GetMonthlySellerSales.Output(monthlySalesList = emptyList()))

        `when`(transactionController.getSellerTransactions(any(), any())).thenReturn(emptyList())

        val monthlySales =
            mapOf(
                "agent-user-1" to
                    listOf(
                        MonthlySellerSale().apply {
                            sellerUserId = "agent-user-1"
                            yearMonth = lastMonthStr
                            sellerSalesAmount = 8000
                            merged = false
                            expirationDate = Instant.now().plusSeconds(86400)
                        },
                        MonthlySellerSale().apply {
                            sellerUserId = "agent-user-1"
                            yearMonth = thisMonthStr
                            sellerSalesAmount = 7000
                            merged = false
                            expirationDate = Instant.now().plusSeconds(86400)
                        },
                    ),
                "test-agent" to
                    listOf(
                        MonthlySellerSale().apply {
                            sellerUserId = "test-agent"
                            yearMonth = lastMonthStr
                            sellerSalesAmount = 4000
                            merged = false
                            expirationDate = Instant.now().plusSeconds(86400)
                        },
                        MonthlySellerSale().apply {
                            sellerUserId = "test-agent"
                            yearMonth = thisMonthStr
                            sellerSalesAmount = 4000
                            merged = false
                            expirationDate = Instant.now().plusSeconds(86400)
                        },
                    ),
                "creator-user-1" to
                    listOf(
                        MonthlySellerSale().apply {
                            sellerUserId = "creator-user-1"
                            yearMonth = lastMonthStr
                            sellerSalesAmount = 3000
                            merged = false
                            expirationDate = Instant.now().plusSeconds(86400)
                        },
                        MonthlySellerSale().apply {
                            sellerUserId = "creator-user-1"
                            yearMonth = thisMonthStr
                            sellerSalesAmount = 2000
                            merged = false
                            expirationDate = Instant.now().plusSeconds(86400)
                        },
                    ),
                "creator-user-2" to
                    listOf(
                        MonthlySellerSale().apply {
                            sellerUserId = "creator-user-2"
                            yearMonth = lastMonthStr
                            sellerSalesAmount = 5000
                            merged = false
                            expirationDate = Instant.now().plusSeconds(86400)
                        },
                        MonthlySellerSale().apply {
                            sellerUserId = "creator-user-2"
                            yearMonth = thisMonthStr
                            sellerSalesAmount = 5000
                            merged = false
                            expirationDate = Instant.now().plusSeconds(86400)
                        },
                    ),
                "test-agent-creator" to
                    listOf(
                        MonthlySellerSale().apply {
                            sellerUserId = "test-agent-creator"
                            yearMonth = lastMonthStr
                            sellerSalesAmount = 15000
                            merged = false
                            expirationDate = Instant.now().plusSeconds(86400)
                        },
                        MonthlySellerSale().apply {
                            sellerUserId = "test-agent-creator"
                            yearMonth = thisMonthStr
                            sellerSalesAmount = 15000
                            merged = false
                            expirationDate = Instant.now().plusSeconds(86400)
                        },
                    ),
            )

        `when`(
                monthlySellerSalesController.getMonthlySales(
                    listOf("creator-user-1", "creator-user-2"),
                    lastMonthStr,
                    thisMonthStr,
                )
            )
            .thenReturn(
                GetMonthlySellerSales.Output(
                    monthlySalesList =
                        (monthlySales.getValue("creator-user-1") +
                                monthlySales.getValue("creator-user-2"))
                            .map {
                                GetMonthlySellerSales.MonthlySales(
                                    sellerUserId = it.sellerUserId,
                                    yearMonth = it.yearMonth ?: "",
                                    sellerSalesAmount = it.sellerSalesAmount,
                                )
                            }
                )
            )

        `when`(
                monthlySellerSalesController.getMonthlySales(
                    listOf("test-agent", "test-agent-creator"),
                    lastMonthStr,
                    thisMonthStr,
                )
            )
            .thenReturn(
                GetMonthlySellerSales.Output(
                    monthlySalesList =
                        monthlySales.getValue("test-agent-creator").map {
                            GetMonthlySellerSales.MonthlySales(
                                sellerUserId = it.sellerUserId,
                                yearMonth = it.yearMonth ?: "",
                                sellerSalesAmount = it.sellerSalesAmount,
                            )
                        }
                )
            )

        `when`(
                monthlySellerSalesController.getMonthlySales(
                    emptyList(),
                    lastMonthStr,
                    thisMonthStr,
                )
            )
            .thenReturn(GetMonthlySellerSales.Output(monthlySalesList = emptyList()))

        // Helper function to create transactions for a given seller and purchaser count
        fun createTransactions(
            sellerUserId: String,
            yearMonth: String,
            purchaseCount: Int,
            purchaserCount: Int,
        ): List<Transaction> {
            val (startDate, endDate) = YearMonthUtil.toMonthPeriod(yearMonth)
            val transactions = mutableListOf<Transaction>()

            val purchasesPerUser = purchaseCount / purchaserCount
            val remainingPurchases = purchaseCount % purchaserCount

            for (i in 1..purchaserCount) {
                val purchaserUserId = "purchaser-$sellerUserId-$i"
                val purchasesForThisUser =
                    if (i <= remainingPurchases) purchasesPerUser + 1 else purchasesPerUser

                for (j in 1..purchasesForThisUser) {
                    transactions.add(
                        TransactionFactory.new(
                            sellerUserId = sellerUserId,
                            purchaserUserId = purchaserUserId,
                            orderedAt = startDate.plusSeconds((i * 1000 + j * 100).toLong()),
                            createdAt = startDate.plusSeconds((i * 1000 + j * 100).toLong()),
                            status = "Success",
                            amount = 100,
                            totalAmount = 100,
                        )
                    )
                }
            }

            return transactions
        }

        `when`(
                transactionController.getSellerTransactions(
                    listOf("creator-user-1", "creator-user-2"),
                    thisMonthStr,
                )
            )
            .thenReturn(
                createTransactions("creator-user-1", thisMonthStr, 250, 200) +
                    createTransactions("creator-user-2", thisMonthStr, 500, 400)
            )

        `when`(
                transactionController.getSellerTransactions(
                    listOf("creator-user-1", "creator-user-2"),
                    lastMonthStr,
                )
            )
            .thenReturn(
                createTransactions("creator-user-1", lastMonthStr, 180, 150) +
                    createTransactions("creator-user-2", lastMonthStr, 360, 290)
            )

        `when`(
                transactionController.getSellerTransactions(
                    listOf("test-agent", "test-agent-creator"),
                    thisMonthStr,
                )
            )
            .thenReturn(createTransactions("test-agent-creator", thisMonthStr, 100, 80))

        `when`(
                transactionController.getSellerTransactions(
                    listOf("test-agent", "test-agent-creator"),
                    lastMonthStr,
                )
            )
            .thenReturn(createTransactions("test-agent-creator", lastMonthStr, 90, 70))

        `when`(transactionController.getSellerTransactions(emptyList(), thisMonthStr))
            .thenReturn(emptyList())

        `when`(transactionController.getSellerTransactions(emptyList(), lastMonthStr))
            .thenReturn(emptyList())
    }
}
