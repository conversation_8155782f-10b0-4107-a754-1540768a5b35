package jp.co.torihada.fanme.endpoints.console.agency

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.ws.rs.core.MediaType
import java.time.YearMonth
import jp.co.torihada.fanme.exception.InvalidYearMonthFormatException
import jp.co.torihada.fanme.exception.PaymentServiceErrorException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.models.Agency
import org.hamcrest.Matchers.`is`
import org.junit.jupiter.api.Test
import org.mockito.Mockito.reset
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`

@QuarkusTest
class AgencyErrorHandlingTest : AgencyEndpointTestBase() {

    @Test
    @TestSecurity(
        user = "super-user-1",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-user-1")],
    )
    fun `無効な日付フォーマットを指定した場合はエラーが返ること`() {
        val agency = Agency.findAll().firstResult()
        if (agency == null) {
            throw IllegalStateException("No agency found for test")
        }
        val agencyId = agency.id.toString()

        val currentYearMonth = YearMonth.now()
        val lastYearMonth = currentYearMonth.minusMonths(1)
        val thisMonthStr = currentYearMonth.toString().replace("-", "")
        val lastMonthStr = lastYearMonth.toString().replace("-", "")

        reset(monthlySellerSalesController)
        reset(transactionController)
        setupMockBalances()

        `when`(
                monthlySellerSalesController.getMonthlySales(
                    listOf("agent-user-1", "creator-user-1", "creator-user-2"),
                    lastMonthStr,
                    thisMonthStr,
                )
            )
            .thenAnswer {
                throw InvalidYearMonthFormatException(
                    "Invalid yearMonth format. Expected format: YYYYMM"
                )
            }

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/agencies/$agencyId/sales")
            .then()
            .statusCode(400)
            .body("errors[0].code", `is`(4004))
            .body("errors[0].message", `is`("Invalid yearMonth format. Expected format: YYYYMM"))

        verify(monthlySellerSalesController, times(1))
            .getMonthlySales(
                listOf("agent-user-1", "creator-user-1", "creator-user-2"),
                lastMonthStr,
                thisMonthStr,
            )
    }

    @Test
    @TestSecurity(
        user = "super-user-1",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-user-1")],
    )
    fun `月次売上データが見つからない場合は404エラーが返ること`() {
        val agency = Agency.findAll().firstResult()
        if (agency == null) {
            throw IllegalStateException("No agency found for test")
        }
        val agencyId = agency.id.toString()

        val currentYearMonth = YearMonth.now()
        val lastYearMonth = currentYearMonth.minusMonths(1)
        val thisMonthStr = currentYearMonth.toString().replace("-", "")
        val lastMonthStr = lastYearMonth.toString().replace("-", "")

        reset(monthlySellerSalesController)
        reset(transactionController)
        setupMockBalances()

        `when`(
                monthlySellerSalesController.getMonthlySales(
                    listOf("agent-user-1", "creator-user-1", "creator-user-2"),
                    lastMonthStr,
                    thisMonthStr,
                )
            )
            .thenAnswer { throw ResourceNotFoundException("MonthlySellerSales") }

        val response =
            given()
                .contentType(MediaType.APPLICATION_JSON)
                .`when`()
                .get("/console/agencies/$agencyId/sales")

        response
            .then()
            .statusCode(404)
            .body("errors[0].code", `is`(1000))
            .body("errors[0].message", `is`("MonthlySellerSales not found."))

        verify(monthlySellerSalesController, times(1))
            .getMonthlySales(
                listOf("agent-user-1", "creator-user-1", "creator-user-2"),
                lastMonthStr,
                thisMonthStr,
            )
    }

    @Test
    @TestSecurity(
        user = "super-user-1",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-user-1")],
    )
    fun `ペイメントサービスからのサーバーエラーが適切にハンドリングされること`() {
        val agency = Agency.findAll().firstResult()
        if (agency == null) {
            throw IllegalStateException("No agency found for test")
        }
        val agencyId = agency.id.toString()

        val currentYearMonth = YearMonth.now()
        val lastYearMonth = currentYearMonth.minusMonths(1)
        val thisMonthStr = currentYearMonth.toString().replace("-", "")
        val lastMonthStr = lastYearMonth.toString().replace("-", "")

        reset(monthlySellerSalesController)
        reset(transactionController)
        setupMockBalances()

        `when`(
                monthlySellerSalesController.getMonthlySales(
                    listOf("agent-user-1", "creator-user-1", "creator-user-2"),
                    lastMonthStr,
                    thisMonthStr,
                )
            )
            .thenAnswer {
                throw PaymentServiceErrorException("Internal server error in payment service")
            }

        val response =
            given()
                .contentType(MediaType.APPLICATION_JSON)
                .`when`()
                .get("/console/agencies/$agencyId/sales")

        response
            .then()
            .statusCode(503)
            .body("errors[0].code", `is`(1202))
            .body("errors[0].message", `is`("Internal server error in payment service"))

        verify(monthlySellerSalesController, times(1))
            .getMonthlySales(
                listOf("agent-user-1", "creator-user-1", "creator-user-2"),
                lastMonthStr,
                thisMonthStr,
            )
    }
}
