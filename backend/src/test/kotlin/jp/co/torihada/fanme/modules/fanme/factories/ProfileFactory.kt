package jp.co.torihada.fanme.modules.fanme.factories

import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.models.Profile
import jp.co.torihada.fanme.modules.fanme.models.User

object ProfileFactory {

    fun new(
        user: User,
        bio: String = "Test bio",
        headerImage: String = "header.jpg",
        snsLinkColor: String = "ORIG",
        officialFlg: Boolean = false,
        themeColorId: Long = Const.ThemeColor.DARK.value,
        coverBrightness: String = Const.Brightness.NORMAL.value,
        coverVisibility: Boolean = true,
        coverResource: String = "cover.jpg",
        coverResourceType: String = Const.CoverResourceType.PHOTO.value,
        snsLinkType: String = Const.SnsLinkType.TWITTER.dbStr,
        snsAccountId: String = "testuser",
        snsDisplayOrderNumber: Int = 1,
        snsDisplayable: Boolean = true,
    ): Profile {
        val profile =
            Profile().apply {
                this.user = user
                this.bio = bio
                this.headerImage = headerImage
                this.snsLinkColor = snsLinkColor
                this.officialFlg = officialFlg
            }

        val themeColor = ProfileThemeColorFactory.new(profile, themeColorId)
        profile.themeColor = themeColor

        val profileCover = ProfileCoverFactory.new(profile, coverBrightness, coverVisibility)

        val coverImage =
            ProfileCoverImageFactory.new(profileCover, coverResource, coverResourceType)

        profileCover.coverImage.add(coverImage)
        profile.cover = profileCover

        val snsLink = SnsLinkFactory.new(profile, snsLinkType, snsAccountId)

        val snsLinkDisplay =
            SnsLinkDisplayFactory.new(profile, snsLink, snsDisplayOrderNumber, snsDisplayable)

        snsLink.snsLinkDisplay = snsLinkDisplay
        profile.snsLinks.add(snsLink)

        return profile
    }

    fun createTestProfile(
        user: User,
        themeColorId: Long = 789L,
        profileCoverId: Long = 101112L,
        coverImageId: Long = 131415L,
        snsLinkId: Long = 161718L,
        snsLinkDisplayId: Long = 192021L,
    ): Profile {
        val profile =
            Profile().apply {
                this.user = user
                this.bio = "Test bio"
                this.headerImage = "header.jpg"
                this.snsLinkColor = "ORIG"
                this.officialFlg = false
            }

        val themeColor =
            ProfileThemeColorFactory.new(profile, Const.ThemeColor.DARK.value).apply {
                this.id = themeColorId
            }
        profile.themeColor = themeColor

        val profileCover = ProfileCoverFactory.new(profile).apply { this.id = profileCoverId }

        val coverImage = ProfileCoverImageFactory.new(profileCover).apply { this.id = coverImageId }

        profileCover.coverImage.add(coverImage)
        profile.cover = profileCover

        val snsLink = SnsLinkFactory.new(profile).apply { this.id = snsLinkId }

        val snsLinkDisplay =
            SnsLinkDisplayFactory.new(profile, snsLink).apply { this.id = snsLinkDisplayId }

        snsLink.snsLinkDisplay = snsLinkDisplay
        profile.snsLinks.add(snsLink)

        return profile
    }
}
