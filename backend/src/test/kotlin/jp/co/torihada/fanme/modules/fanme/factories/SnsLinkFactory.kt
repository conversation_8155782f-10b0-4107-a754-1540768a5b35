package jp.co.torihada.fanme.modules.fanme.factories

import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.models.Profile
import jp.co.torihada.fanme.modules.fanme.models.SnsLink

object SnsLinkFactory {
    fun new(
        profile: Profile,
        type: String = Const.SnsLinkType.TWITTER.dbStr,
        snsAccountId: String = "testuser",
    ): SnsLink {
        return SnsLink().apply {
            this.profile = profile
            this.type = type
            this.snsAccountId = snsAccountId
        }
    }
}
