package jp.co.torihada.fanme.modules.fanme.usecases.profile

import com.github.michaelbull.result.unwrap
import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.factories.ProfileFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

@QuarkusTest
class GetProfileTest {

    @Inject private lateinit var getProfile: GetProfile

    @Test
    @TestTransaction
    fun `execute returns profile when user exists`() {
        val user =
            UserFactory.createTestUser(
                uid = "test-user-uid",
                name = "Test User",
                accountIdentity = "test-user-account-identity",
            ) ?: throw IllegalStateException("Failed to create test user")
        user.persistAndFlush()
        val profile = ProfileFactory.createTestProfile(user = user)
        profile.persistAndFlush()

        val input = GetProfile.Input(userId = user.id!!)
        val result = getProfile.execute(input)

        assertTrue(result.isOk)
        val profileResult = result.unwrap()
        assertNotNull(profileResult)
        assertEquals(user.id, profileResult.userId)
        assertEquals(profile.id, profileResult.id)
        assertEquals(profile.bio, profileResult.bio)
        assertNotNull(profileResult.coverImage)
        assertEquals(
            profile.cover?.coverImage?.first()?.resourceType,
            profileResult.coverImage?.resourceType,
        )
        assertEquals(profile.snsLinkColor, profileResult.snsLinkColor)
        assertEquals(
            Const.ThemeColor.fromValue(profile.themeColor!!.themeColorId)?.str,
            profileResult.themeColor,
        )
        assertEquals(profile.snsLinks.size, profileResult.snsLinks.size)
        profile.snsLinks.forEachIndexed { index, snsLink ->
            assertEquals(snsLink.id, profileResult.snsLinks[index].id)
            assertEquals(
                Const.SnsLinkType.fromDbStr(snsLink.type!!)?.value,
                profileResult.snsLinks[index].type,
            )
            assertEquals(snsLink.snsAccountId, profileResult.snsLinks[index].snsAccountId)
            assertEquals(
                snsLink.snsLinkDisplay?.displayOrderNumber,
                profileResult.snsLinks[index].displayOrderNumber,
            )
            assertEquals(
                snsLink.snsLinkDisplay?.displayable,
                profileResult.snsLinks[index].displayable,
            )
        }
    }
}
