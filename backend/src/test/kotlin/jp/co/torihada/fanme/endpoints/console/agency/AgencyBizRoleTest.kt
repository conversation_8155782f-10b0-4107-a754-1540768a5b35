package jp.co.torihada.fanme.endpoints.console.agency

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.models.Agency
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.notNullValue
import org.junit.jupiter.api.Test

@QuarkusTest
class AgencyBizRoleTest : AgencyEndpointTestBase() {

    @Test
    @TestSecurity(
        user = "biz-user-1",
        roles = [UserRole.BIZ_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "biz-user-1")],
    )
    fun `BIZロールでエージェンシー一覧を取得できること（全てのエージェンシー）`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/agencies")
            .then()
            .statusCode(200)
            .body(notNullValue())
            .body("data.agencies.size()", equalTo(3))
    }

    @Test
    @TestSecurity(
        user = "biz-user-1",
        roles = [UserRole.BIZ_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "biz-user-1")],
    )
    fun `BIZロールでエージェンシーの売上データを取得できること`() {
        val agency = Agency.findAll().firstResult()
        if (agency == null) {
            throw IllegalStateException("No agency found for test")
        }
        val agencyId = agency.id.toString()

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/agencies/$agencyId/sales")
            .then()
            .statusCode(200)
            .body(notNullValue())
            .body("data.agencySales.usersSales.size()", equalTo(3))
    }
}
