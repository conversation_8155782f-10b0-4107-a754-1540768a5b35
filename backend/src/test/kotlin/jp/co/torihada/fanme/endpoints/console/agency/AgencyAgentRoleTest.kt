package jp.co.torihada.fanme.endpoints.console.agency

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.models.Agency
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.notNullValue
import org.junit.jupiter.api.Test

@QuarkusTest
class AgencyAgentRoleTest : AgencyEndpointTestBase() {

    @Test
    @TestSecurity(
        user = "test-agent",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "test-agent")],
    )
    fun `認可されていないagency idのデータにはアクセスできないこと`() {
        val agencies = Agency.findAll().list()
        val unauthorizedAgency = agencies.find { it.name == "Unauthorized Agency" }
        if (unauthorizedAgency == null) {
            throw IllegalStateException("Unauthorized agency not found for test")
        }
        val unauthorizedAgencyId = unauthorizedAgency.id.toString()

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/agencies/$unauthorizedAgencyId/sales")
            .then()
            .statusCode(403)
    }

    @Test
    @TestSecurity(
        user = "test-agent",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "test-agent")],
    )
    fun `AGENTロールでエージェンシー一覧を取得できること（自分のエージェンシーのみ）`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/agencies")
            .then()
            .statusCode(200)
            .body(notNullValue())
            .body("data.agencies.size()", equalTo(1))
            .body("data.agencies[0].name", equalTo("Test Agent Agency"))
    }

    @Test
    @TestSecurity(
        user = "test-agent",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "test-agent")],
    )
    fun `AGENTロールでエージェンシーの売上データを取得できること`() {
        val agencies = Agency.findAll().list()
        val testAgentAgency = agencies.find { it.name == "Test Agent Agency" }
        if (testAgentAgency == null) {
            throw IllegalStateException("Test Agent Agency not found for test")
        }
        val agencyId = testAgentAgency.id.toString()

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/agencies/$agencyId/sales")
            .then()
            .statusCode(200)
            .body(notNullValue())
            .body("data.agencySales.usersSales.size()", equalTo(2))
            .body("data.agencySales.totalSales.thisMonth.sellerSalesAmount", equalTo(15000))
            .body("data.agencySales.totalSales.thisMonth.purchaseCount", equalTo(100))
            .body("data.agencySales.totalSales.thisMonth.purchaserCount", equalTo(80))
            .body("data.agencySales.totalSales.lastMonth.sellerSalesAmount", equalTo(15000))
            .body("data.agencySales.totalSales.lastMonth.purchaseCount", equalTo(90))
            .body("data.agencySales.totalSales.lastMonth.purchaserCount", equalTo(70))
    }
}
