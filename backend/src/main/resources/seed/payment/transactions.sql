INSERT IGNORE INTO transactions (`tenant`, `purchaser_user_id`, `unique_order_id`, `seller_user_id`, `ordered_at`, `amount`, `total_amount`, `status`, `payment_type`, `created_at`, `updated_at`) VALUES 
-- Current month transactions for test-creator-1
('fanme', 'test-purchaser-1', 'ORD-2024-01-001', 'test-creator-1', NOW(), 5000, 5500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-1', 'ORD-2024-01-002', 'test-creator-1', NOW(), 3000, 3300, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-2', 'ORD-2024-01-003', 'test-creator-1', NOW(), 10000, 11000, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-3', 'ORD-2024-01-004', 'test-creator-1', NOW(), 7500, 8250, 'success', 'paypay', NOW(), NOW()),
('fanme', 'test-purchaser-4', 'ORD-2024-01-005', 'test-creator-1', NOW(), 2000, 2200, 'failed', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-5', 'ORD-2024-01-006', 'test-creator-1', NOW(), 15000, 16500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-5', 'ORD-2024-01-007', 'test-creator-1', NOW(), 9500, 10450, 'success', 'credit_card', NOW(), NOW()),

-- Current month transactions for test-creator-2
('fanme', 'test-purchaser-1', 'ORD-2024-01-008', 'test-creator-2', NOW(), 4000, 4400, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-6', 'ORD-2024-01-009', 'test-creator-2', NOW(), 6000, 6600, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-7', 'ORD-2024-01-010', 'test-creator-2', NOW(), 8000, 8800, 'success', 'paypay', NOW(), NOW()),
('fanme', 'test-purchaser-8', 'ORD-2024-01-011', 'test-creator-2', NOW(), 12000, 13200, 'success', 'credit_card', NOW(), NOW()),

-- Last month transactions for test-creator-1
('fanme', 'test-purchaser-1', 'ORD-2024-00-001', 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 5000, 5500, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-2', 'ORD-2024-00-002', 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 7000, 7700, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-2', 'ORD-2024-00-003', 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 3000, 3300, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-9', 'ORD-2024-00-004', 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 10000, 11000, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-10', 'ORD-2024-00-005', 'test-creator-1', DATE_SUB(NOW(), INTERVAL 1 MONTH), 20000, 22000, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),

-- Last month transactions for test-creator-2
('fanme', 'test-purchaser-6', 'ORD-2024-00-006', 'test-creator-2', DATE_SUB(NOW(), INTERVAL 1 MONTH), 3500, 3850, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-7', 'ORD-2024-00-007', 'test-creator-2', DATE_SUB(NOW(), INTERVAL 1 MONTH), 4500, 4950, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-7', 'ORD-2024-00-008', 'test-creator-2', DATE_SUB(NOW(), INTERVAL 1 MONTH), 5500, 6050, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-8', 'ORD-2024-00-009', 'test-creator-2', DATE_SUB(NOW(), INTERVAL 1 MONTH), 11500, 12650, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),

-- 2 months ago transactions (for accumulated sales testing)
('fanme', 'test-purchaser-1', 'ORD-2023-99-001', 'test-creator-1', DATE_SUB(NOW(), INTERVAL 2 MONTH), 8000, 8800, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 2 MONTH), DATE_SUB(NOW(), INTERVAL 2 MONTH)),
('fanme', 'test-purchaser-11', 'ORD-2023-99-002', 'test-creator-1', DATE_SUB(NOW(), INTERVAL 2 MONTH), 12000, 13200, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 2 MONTH), DATE_SUB(NOW(), INTERVAL 2 MONTH)),
('fanme', 'test-purchaser-12', 'ORD-2023-99-003', 'test-creator-1', DATE_SUB(NOW(), INTERVAL 2 MONTH), 20000, 22000, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 2 MONTH), DATE_SUB(NOW(), INTERVAL 2 MONTH)),

-- Failed and pending transactions (should not be counted)
('fanme', 'test-purchaser-13', 'ORD-2024-01-F01', 'test-creator-1', NOW(), 5000, 5500, 'failed', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-14', 'ORD-2024-01-P01', 'test-creator-1', NOW(), 7000, 7700, 'pending', 'credit_card', NOW(), NOW()),

-- Transactions for agency testing (test-creator-3 to test-creator-8 are in the same agency)
-- Current month
('fanme', 'test-purchaser-15', 'ORD-2024-01-101', 'test-creator-3', NOW(), 3000, 3300, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-15', 'ORD-2024-01-102', 'test-creator-3', NOW(), 4000, 4400, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-16', 'ORD-2024-01-103', 'test-creator-3', NOW(), 5000, 5500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-17', 'ORD-2024-01-104', 'test-creator-3', NOW(), 8000, 8800, 'success', 'paypay', NOW(), NOW()),

('fanme', 'test-purchaser-15', 'ORD-2024-01-105', 'test-creator-4', NOW(), 6000, 6600, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-18', 'ORD-2024-01-106', 'test-creator-4', NOW(), 7000, 7700, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-19', 'ORD-2024-01-107', 'test-creator-4', NOW(), 9000, 9900, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-20', 'ORD-2024-01-108', 'test-creator-4', NOW(), 10000, 11000, 'success', 'paypay', NOW(), NOW()),
('fanme', 'test-purchaser-20', 'ORD-2024-01-109', 'test-creator-4', NOW(), 8000, 8800, 'success', 'credit_card', NOW(), NOW()),

-- Last month for agency testing
('fanme', 'test-purchaser-15', 'ORD-2024-00-101', 'test-creator-3', DATE_SUB(NOW(), INTERVAL 1 MONTH), 2500, 2750, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-16', 'ORD-2024-00-102', 'test-creator-3', DATE_SUB(NOW(), INTERVAL 1 MONTH), 3500, 3850, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-21', 'ORD-2024-00-103', 'test-creator-3', DATE_SUB(NOW(), INTERVAL 1 MONTH), 4500, 4950, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-22', 'ORD-2024-00-104', 'test-creator-3', DATE_SUB(NOW(), INTERVAL 1 MONTH), 5500, 6050, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),

('fanme', 'test-purchaser-15', 'ORD-2024-00-105', 'test-creator-4', DATE_SUB(NOW(), INTERVAL 1 MONTH), 6500, 7150, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-18', 'ORD-2024-00-106', 'test-creator-4', DATE_SUB(NOW(), INTERVAL 1 MONTH), 7500, 8250, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-23', 'ORD-2024-00-107', 'test-creator-4', DATE_SUB(NOW(), INTERVAL 1 MONTH), 8500, 9350, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-23', 'ORD-2024-00-108', 'test-creator-4', DATE_SUB(NOW(), INTERVAL 1 MONTH), 12500, 13750, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),

-- Transactions for test-creator-5 (to match sales amounts)
-- Current month (sellerSalesAmount: 42000)
('fanme', 'test-purchaser-24', 'ORD-2024-01-201', 'test-creator-5', NOW(), 10000, 11000, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-24', 'ORD-2024-01-202', 'test-creator-5', NOW(), 8000, 8800, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-25', 'ORD-2024-01-203', 'test-creator-5', NOW(), 12000, 13200, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-26', 'ORD-2024-01-204', 'test-creator-5', NOW(), 15000, 16500, 'success', 'paypay', NOW(), NOW()),
('fanme', 'test-purchaser-27', 'ORD-2024-01-205', 'test-creator-5', NOW(), 5000, 5500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-28', 'ORD-2024-01-206', 'test-creator-5', NOW(), 10000, 11000, 'success', 'credit_card', NOW(), NOW()),

-- Last month for test-creator-5 (sellerSalesAmount: 38500)
('fanme', 'test-purchaser-24', 'ORD-2024-00-201', 'test-creator-5', DATE_SUB(NOW(), INTERVAL 1 MONTH), 9000, 9900, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-25', 'ORD-2024-00-202', 'test-creator-5', DATE_SUB(NOW(), INTERVAL 1 MONTH), 11000, 12100, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-29', 'ORD-2024-00-203', 'test-creator-5', DATE_SUB(NOW(), INTERVAL 1 MONTH), 7000, 7700, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-30', 'ORD-2024-00-204', 'test-creator-5', DATE_SUB(NOW(), INTERVAL 1 MONTH), 13000, 14300, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-31', 'ORD-2024-00-205', 'test-creator-5', DATE_SUB(NOW(), INTERVAL 1 MONTH), 15000, 16500, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),

-- Transactions for test-creator-6 (to match sales amounts)
-- Current month (sellerSalesAmount: 52500)
('fanme', 'test-purchaser-32', 'ORD-2024-01-301', 'test-creator-6', NOW(), 15000, 16500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-32', 'ORD-2024-01-302', 'test-creator-6', NOW(), 12000, 13200, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-33', 'ORD-2024-01-303', 'test-creator-6', NOW(), 18000, 19800, 'success', 'paypay', NOW(), NOW()),
('fanme', 'test-purchaser-34', 'ORD-2024-01-304', 'test-creator-6', NOW(), 20000, 22000, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-35', 'ORD-2024-01-305', 'test-creator-6', NOW(), 10000, 11000, 'success', 'credit_card', NOW(), NOW()),

-- Last month for test-creator-6 (sellerSalesAmount: 49000)
('fanme', 'test-purchaser-32', 'ORD-2024-00-301', 'test-creator-6', DATE_SUB(NOW(), INTERVAL 1 MONTH), 14000, 15400, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-36', 'ORD-2024-00-302', 'test-creator-6', DATE_SUB(NOW(), INTERVAL 1 MONTH), 16000, 17600, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-37', 'ORD-2024-00-303', 'test-creator-6', DATE_SUB(NOW(), INTERVAL 1 MONTH), 19000, 20900, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-38', 'ORD-2024-00-304', 'test-creator-6', DATE_SUB(NOW(), INTERVAL 1 MONTH), 21000, 23100, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),

-- Transactions for test-creator-7 (to match sales amounts)
-- Current month (sellerSalesAmount: 63000)
('fanme', 'test-purchaser-39', 'ORD-2024-01-401', 'test-creator-7', NOW(), 20000, 22000, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-39', 'ORD-2024-01-402', 'test-creator-7', NOW(), 15000, 16500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-40', 'ORD-2024-01-403', 'test-creator-7', NOW(), 25000, 27500, 'success', 'paypay', NOW(), NOW()),
('fanme', 'test-purchaser-41', 'ORD-2024-01-404', 'test-creator-7', NOW(), 30000, 33000, 'success', 'credit_card', NOW(), NOW()),

-- Last month for test-creator-7 (sellerSalesAmount: 59500)
('fanme', 'test-purchaser-39', 'ORD-2024-00-401', 'test-creator-7', DATE_SUB(NOW(), INTERVAL 1 MONTH), 18000, 19800, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-42', 'ORD-2024-00-402', 'test-creator-7', DATE_SUB(NOW(), INTERVAL 1 MONTH), 22000, 24200, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-43', 'ORD-2024-00-403', 'test-creator-7', DATE_SUB(NOW(), INTERVAL 1 MONTH), 25000, 27500, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-44', 'ORD-2024-00-404', 'test-creator-7', DATE_SUB(NOW(), INTERVAL 1 MONTH), 20000, 22000, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),

-- Transactions for test-creator-8 (to match sales amounts)
-- Current month (sellerSalesAmount: 70000)
('fanme', 'test-purchaser-45', 'ORD-2024-01-501', 'test-creator-8', NOW(), 25000, 27500, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-45', 'ORD-2024-01-502', 'test-creator-8', NOW(), 20000, 22000, 'success', 'credit_card', NOW(), NOW()),
('fanme', 'test-purchaser-46', 'ORD-2024-01-503', 'test-creator-8', NOW(), 30000, 33000, 'success', 'paypay', NOW(), NOW()),
('fanme', 'test-purchaser-47', 'ORD-2024-01-504', 'test-creator-8', NOW(), 25000, 27500, 'success', 'credit_card', NOW(), NOW()),

-- Last month for test-creator-8 (sellerSalesAmount: 66500)
('fanme', 'test-purchaser-45', 'ORD-2024-00-501', 'test-creator-8', DATE_SUB(NOW(), INTERVAL 1 MONTH), 22000, 24200, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-48', 'ORD-2024-00-502', 'test-creator-8', DATE_SUB(NOW(), INTERVAL 1 MONTH), 28000, 30800, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-49', 'ORD-2024-00-503', 'test-creator-8', DATE_SUB(NOW(), INTERVAL 1 MONTH), 35000, 38500, 'success', 'paypay', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH)),
('fanme', 'test-purchaser-50', 'ORD-2024-00-504', 'test-creator-8', DATE_SUB(NOW(), INTERVAL 1 MONTH), 10000, 11000, 'success', 'credit_card', DATE_SUB(NOW(), INTERVAL 1 MONTH), DATE_SUB(NOW(), INTERVAL 1 MONTH));