package jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.modules.shop.controllers.requests.ItemRequest
import jp.co.torihada.fanme.modules.shop.models.ItemType

data class CreateOrUpdateItemRequest(
    @JsonProperty("name") val name: String,
    @JsonProperty("description") val description: String?,
    @JsonProperty("thumbnail_uri") val thumbnailUri: String,
    @JsonProperty("thumbnail_from") val thumbnailFrom: Int,
    @JsonProperty("thumbnail_blur_level") val thumbnailBlurLevel: Int,
    @JsonProperty("thumbnail_watermark_level") val thumbnailWatermarkLevel: Int,
    @JsonProperty("price") val price: Int,
    @JsonProperty("available") val available: <PERSON>ole<PERSON>,
    @JsonProperty("item_files") val itemFiles: List<File>,
    @JsonProperty("sample_files") val samples: List<File>?,
    @JsonProperty("benefit") val benefit: Benefit?,
    @JsonProperty("tags") val tags: List<String>?,
    @JsonProperty("item_option") val itemOption: ItemOption,
    @JsonProperty("item_type") val itemType: Int? = ItemType.DIGITAL_BUNDLE.value,
)

data class File(
    @JsonProperty("id") val id: Long?,
    @JsonProperty("name") val name: String,
    @JsonProperty("object_uri") val objectUri: String,
    @JsonProperty("thumbnail_uri") val thumbnailUri: String?,
    @JsonProperty("masked_thumbnail_uri") val maskedThumbnailUri: String?,
    @JsonProperty("price") val price: Int?,
    @JsonProperty("file_type") val fileType: String,
    @JsonProperty("size") val size: Float,
    @JsonProperty("duration") val duration: Int,
    @JsonProperty("item_thumbnail_selected") val itemThumbnailSelected: Boolean?,
    @JsonProperty("sort_order") val sortOrder: Int,
)

data class Benefit(
    @JsonProperty("description") val description: String?,
    @JsonProperty("files") val files: List<File>,
)

data class ItemOption(
    @JsonAlias("is_single_sales") val isSingleSales: Boolean,
    @JsonAlias("qty_total") val qtyTotal: Int?,
    @JsonAlias("qty_per_user") val qtyPerUser: Int?,
    @JsonAlias("for_sale") val forSale: ForSale?,
    @JsonAlias("password") val password: String?,
    @JsonAlias("on_sale") val onSale: OnSale?,
)

data class ForSale(
    @JsonAlias("start_at") val startAt: String?,
    @JsonAlias("end_at") val endAt: String?,
)

data class OnSale(
    @JsonAlias("discount_rate") val discountRate: Float,
    @JsonAlias("start_at") val startAt: String?,
    @JsonAlias("end_at") val endAt: String?,
)

data class SortItemsRequest(@JsonProperty("items") val items: List<SortItem>)

data class SortItem(
    @JsonProperty("id") val id: Long,
    @JsonProperty("sort_order") val sortOrder: Int,
)

@ApplicationScoped
class RequestConverter() {
    fun requestToCreateItem(
        uid: String,
        request: CreateOrUpdateItemRequest,
    ): ItemRequest.CreateItem {
        return ItemRequest.CreateItem(
            creatorUid = uid,
            name = request.name,
            description = request.description,
            thumbnailUri = request.thumbnailUri,
            thumbnailFrom = request.thumbnailFrom,
            thumbnailBlurLevel = request.thumbnailBlurLevel,
            thumbnailWatermarkLevel = request.thumbnailWatermarkLevel,
            price = request.price,
            available = request.available,
            files = convertFiles(request.itemFiles),
            samples = request.samples?.let { convertFiles(it) },
            benefit = convertBenefit(request.benefit),
            tags = request.tags,
            itemOption = convertItemOption(request.itemOption),
            itemType = request.itemType?.let { ItemType.fromValue(it) } ?: ItemType.DIGITAL_BUNDLE,
        )
    }

    fun requestToUpdateItem(
        uid: String,
        id: Long,
        request: CreateOrUpdateItemRequest,
    ): ItemRequest.UpdateItem {
        return ItemRequest.UpdateItem(
            creatorUid = uid,
            itemId = id,
            name = request.name,
            description = request.description,
            thumbnailUri = request.thumbnailUri,
            thumbnailFrom = request.thumbnailFrom,
            thumbnailBlurLevel = request.thumbnailBlurLevel,
            thumbnailWatermarkLevel = request.thumbnailWatermarkLevel,
            price = request.price,
            available = request.available,
            files = convertFiles(request.itemFiles),
            samples = request.samples?.let { convertFiles(it) },
            benefit = convertBenefit(request.benefit),
            tags = request.tags,
            itemOption = convertItemOption(request.itemOption),
        )
    }

    private fun convertFiles(request: List<File>): List<ItemRequest.File> {
        return request.map {
            ItemRequest.File(
                id = it.id,
                name = it.name,
                objectUri = it.objectUri,
                thumbnailUri = it.thumbnailUri,
                maskedThumbnailUri = it.maskedThumbnailUri,
                price = it.price,
                fileType = it.fileType,
                size = it.size,
                duration = it.duration,
                itemThumbnailSelected = it.itemThumbnailSelected,
                sortOrder = it.sortOrder,
            )
        }
    }

    private fun convertBenefit(request: Benefit?): ItemRequest.Benefit? {
        return request?.let {
            ItemRequest.Benefit(description = it.description, files = convertFiles(it.files))
        }
    }

    private fun convertForSale(request: ForSale?): ItemRequest.ForSale? {
        return request?.let {
            ItemRequest.ForSale(startAt = request.startAt, endAt = request.endAt)
        }
    }

    private fun convertOnSale(request: OnSale?): ItemRequest.OnSale? {
        return request?.let {
            ItemRequest.OnSale(
                discountRate = request.discountRate,
                startAt = request.startAt,
                endAt = request.endAt,
            )
        }
    }

    private fun convertItemOption(request: ItemOption): ItemRequest.ItemOption {
        return ItemRequest.ItemOption(
            isSingleSales = request.isSingleSales,
            qtyTotal = request.qtyTotal,
            qtyPerUser = request.qtyPerUser,
            forSale = convertForSale(request.forSale),
            password = request.password,
            onSale = convertOnSale(request.onSale),
        )
    }

    fun requestToSortItems(userUid: String, request: SortItemsRequest): ItemRequest.SortItems {
        return ItemRequest.SortItems(
            userUid = userUid,
            items = request.items.map { ItemRequest.SortItem(it.id, it.sortOrder) },
        )
    }
}
