package jp.co.torihada.fanme.modules.payment.usecases.monthlysales

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.payment.models.MonthlySellerSale

@ApplicationScoped
class GetMonthlySellerSales {

    data class Input(
        val sellerUserIds: List<String>,
        val fromYearMonth: String? = null,
        val toYearMonth: String? = null,
    )

    data class MonthlySales(
        val sellerUserId: String,
        val yearMonth: String,
        val sellerSalesAmount: Int,
    )

    data class Output(val monthlySalesList: List<MonthlySales>)

    fun execute(input: Input): Result<Output, FanmeException> {

        val allMonthlySalesList =
            MonthlySellerSale.findBySellerUserIds(
                input.sellerUserIds,
                input.fromYearMonth,
                input.toYearMonth,
            )

        val monthlySalesList =
            allMonthlySalesList.map { monthlySales ->
                MonthlySales(
                    sellerUserId = monthlySales.sellerUserId,
                    yearMonth = monthlySales.yearMonth ?: "",
                    sellerSalesAmount = monthlySales.sellerSalesAmount,
                )
            }

        return Ok(Output(monthlySalesList))
    }
}
