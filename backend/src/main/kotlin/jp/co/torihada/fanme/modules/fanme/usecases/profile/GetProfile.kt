package jp.co.torihada.fanme.modules.fanme.usecases.profile

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.Config
import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.models.Profile as ProfileModel
import org.eclipse.microprofile.openapi.annotations.media.Schema

@ApplicationScoped
class GetProfile {

    @Inject private lateinit var fanmeConfig: Config

    data class Input(val userId: Long)

    @Schema(name = "GetProfile_Profile")
    data class Profile(
        val id: Long,
        val userId: Long,
        val bio: String?,
        val coverImage: CoverImage?,
        val headerImage: String?,
        val snsLinkColor: String?,
        val snsLinks: List<SnsLink>,
        val themeColor: String?,
    ) {
        @Schema(name = "GetProfile_CoverImage")
        data class CoverImage(
            val resourceType: String,
            val resource: String,
            val coverVisibility: Boolean,
            val brightness: String,
        )

        @Schema(name = "GetProfile_SnsLink")
        data class SnsLink(
            val id: Long,
            val type: String,
            val snsAccountId: String,
            val displayOrderNumber: Int,
            val displayable: Boolean,
        )
    }

    fun execute(input: Input): Result<Profile, Exception> {
        val profile =
            ProfileModel.findByUserId(input.userId) ?: return Err(ResourceNotFoundException("User"))

        val result =
            Profile(
                id = profile.id!!,
                userId = profile.user?.id!!,
                bio = profile.bio,
                coverImage =
                    if (profile.cover != null && !profile.cover?.coverImage.isNullOrEmpty()) {
                        Profile.CoverImage(
                            resourceType = profile.cover?.coverImage?.first()?.resourceType ?: "",
                            resource =
                                profile.cover?.coverImage?.first().let {
                                    "${fanmeConfig.s3Endpoint()}/${fanmeConfig.s3BucketName()}/${Const.S3_PATH}/profile_cover_image/resource/${it?.id}/${it?.resource}"
                                },
                            coverVisibility = profile.cover?.coverVisibility ?: false,
                            brightness = profile.cover?.brightness ?: Const.Brightness.NORMAL.value,
                        )
                    } else null,
                headerImage =
                    profile.headerImage?.let {
                        "${fanmeConfig.s3Endpoint()}/${fanmeConfig.s3BucketName()}/${Const.S3_PATH}/profile/header_image/icon/${profile.id}/$it"
                    },
                snsLinkColor = profile.snsLinkColor,
                snsLinks =
                    profile.snsLinks.map { snsLink ->
                        Profile.SnsLink(
                            id = snsLink.id!!,
                            type = Const.SnsLinkType.fromDbStr(snsLink.type!!)?.value ?: "",
                            snsAccountId = snsLink.snsAccountId ?: "",
                            displayOrderNumber = snsLink.snsLinkDisplay?.displayOrderNumber ?: 0,
                            displayable = snsLink.snsLinkDisplay?.displayable ?: false,
                        )
                    },
                themeColor =
                    when (profile.themeColor?.themeColorId) {
                        Const.ThemeColor.DARK.value -> Const.ThemeColor.DARK.str
                        Const.ThemeColor.LIGHT.value -> Const.ThemeColor.LIGHT.str
                        Const.ThemeColor.CUSTOM.value -> profile.themeColor?.customColor ?: ""
                        else -> ""
                    },
            )

        return Ok(result)
    }
}
