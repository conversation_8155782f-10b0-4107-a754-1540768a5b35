package jp.co.torihada.fanme.endpoints.fanme

import com.fasterxml.jackson.annotation.JsonProperty
import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.Consumes
import jakarta.ws.rs.GET
import jakarta.ws.rs.POST
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.QueryParam
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.Const.ContentBlockType
import jp.co.torihada.fanme.dto.ContentBlockResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.lib.MaskConfig
import jp.co.torihada.fanme.modules.fanme.controllers.ContentBlockController
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/fanme/content_blocks")
@Tag(name = "FANME", description = "FANME APIサーバー")
class ContentBlockEndpoint {

    @Inject lateinit var securityIdentity: SecurityIdentity
    @Inject lateinit var handler: ContentBlockController
    @Inject lateinit var util: Util
    @Inject lateinit var maskConfig: MaskConfig

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(ContentBlockResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getContentBlocks(
        /* TODO 本来はQueryParamではなくPathParamで取得する(/fanme/{creator_account_identity}/content_blocks)
         * しかし、createの方のパスも変わるためデグレを避けるためにQueryParamで暫定対応
         */
        @QueryParam("creator_account_identity") creatorAccountIdentity: String
    ): Response {
        return try {
            val contentBlocks = handler.getUserContentBlocks(creatorAccountIdentity)
            val entity = ResponseEntity(contentBlocks, "content_blocks")

            maskConfig.enabled = true
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    // TODO フロントエンドの移行時に、CurrentUserContentBlockEndpointを作成して/fanme/current/content_blocksに変更する
    @Path("/current")
    @GET
    @RolesAllowed("LoginUser")
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(ContentBlockResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getCurrentUserContentBlocks(): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val contentBlocks = handler.getCurrentUserContentBlocks(userUid)
            val entity = ResponseEntity(contentBlocks, "content_blocks")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    data class CreateContentWithDetailRequest(
        @JsonProperty("content_block_type") val contentBlockType: Long,
        @JsonProperty("title") val title: String,
        @JsonProperty("description") val description: String?,
        @JsonProperty("app_description") val appDescription: String?,
        @JsonProperty("url") val url: String,
        @JsonProperty("icon_url") val iconUrl: String?,
    )

    @POST
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/create_with_detail")
    fun createWithDetail(requestBody: CreateContentWithDetailRequest): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()

            val blockType = ContentBlockType.fromId(requestBody.contentBlockType)
            if (blockType == null) throw FanmeException(0, "Invalid content block type")

            val result =
                handler.createContentBlockWithDetail(
                    ContentBlockController.CreateContentBlockWithDetailRequest(
                        creatorUid = userUid,
                        contentBlockType = blockType,
                        title = requestBody.title,
                        description = requestBody.description,
                        appDescription = requestBody.appDescription,
                        url = requestBody.url,
                        iconUrl = requestBody.iconUrl,
                    )
                )
            val entity = ResponseEntity(result, "content_block")
            return Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
