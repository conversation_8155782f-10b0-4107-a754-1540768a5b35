package jp.co.torihada.fanme.modules.fanme.controllers

import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.usecases.profile.GetProfile

@ApplicationScoped
class ProfileController {
    @Inject private lateinit var getProfile: GetProfile

    fun getUserProfile(user: User): GetProfile.Profile {
        return getProfile.execute(GetProfile.Input(userId = user.id!!)).getOrElse { throw it }
    }
}
