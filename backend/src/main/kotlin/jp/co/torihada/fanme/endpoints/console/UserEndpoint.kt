package jp.co.torihada.fanme.endpoints.console

import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.PathParam
import jakarta.ws.rs.Produces
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.BaseResponseBody
import jp.co.torihada.fanme.dto.ConsoleUserResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.exception.ConsoleException
import jp.co.torihada.fanme.exception.ConsoleResourceNotFoundException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.IUserController
import org.eclipse.microprofile.openapi.annotations.media.Content
import org.eclipse.microprofile.openapi.annotations.media.Schema
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponses
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/console/users")
@Tag(name = "CONSOLE", description = "CONSOLE APIサーバー")
class UserEndpoint {

    @Inject lateinit var handler: IUserController

    @GET
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE)
    @APIResponses(
        APIResponse(
            responseCode = "200",
            description = "クリエイター情報の取得成功",
            content = [Content(schema = Schema(implementation = ConsoleUserResponseBody::class))],
        ),
        APIResponse(
            responseCode = "400",
            description = "不正なリクエスト",
            content = [Content(schema = Schema(implementation = BaseResponseBody::class))],
        ),
        APIResponse(
            responseCode = "403",
            description = "アクセス権限がありません",
            content = [Content(schema = Schema(implementation = BaseResponseBody::class))],
        ),
        APIResponse(
            responseCode = "404",
            description = "指定されたIDのクリエイターが見つかりません",
            content = [Content(schema = Schema(implementation = BaseResponseBody::class))],
        ),
        APIResponse(
            responseCode = "500",
            description = "サーバー内部エラー",
            content = [Content(schema = Schema(implementation = BaseResponseBody::class))],
        ),
    )
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/{id}")
    fun getById(@PathParam("id") id: Long): Response {
        return try {
            val userDetail = handler.getUser(id)

            val responseEntityToFilter =
                ResponseEntity(entity = userDetail, topLevelPropertyName = "user")

            Response.ok(responseEntityToFilter).build()
        } catch (e: ConsoleResourceNotFoundException) {
            val responseBody = BaseResponseBody(errors = e.errors, data = null)
            Response.status(Response.Status.NOT_FOUND).entity(responseBody).build()
        } catch (e: ResourceNotFoundException) {
            val responseBody = BaseResponseBody(errors = e.errors, data = null)
            Response.status(Response.Status.NOT_FOUND).entity(responseBody).build()
        } catch (e: ConsoleException) {
            val responseBody = BaseResponseBody(errors = e.errors, data = null)
            Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(responseBody).build()
        }
    }
}
