package jp.co.torihada.fanme.batch.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.nio.file.Files
import java.nio.file.Path
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.modules.fanme.controllers.FanmeCustomerController
import jp.co.torihada.fanme.modules.shop.Config as ShopConfig
import jp.co.torihada.fanme.modules.shop.Const
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.external.GcpStorageClient
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem
import jp.co.torihada.fanme.modules.shop.services.aws.S3
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger

@ApplicationScoped
class OrderDeliveryInfoCsvBatch {

    @Inject private lateinit var logger: Logger
    @Inject private lateinit var config: Config
    @Inject private lateinit var shopConfig: ShopConfig
    @Inject private lateinit var s3: S3
    @Inject private lateinit var fanmeCustomerController: FanmeCustomerController

    @RestClient private lateinit var gcpStorageClient: GcpStorageClient

    fun execute(targetDate: LocalDate) {
        val jstZone = ZoneId.of("Asia/Tokyo")

        val startTimeJst = targetDate.atStartOfDay(jstZone)
        val endTimeJst = startTimeJst.plusDays(1)

        // DBにはUTCで保存されているため、JSTの0時をUTCに変換
        val startDateTimeUtc = startTimeJst.toInstant()
        val endDateTimeUtc = endTimeJst.toInstant()

        logger.info(
            "Search purchasedItems with successful payment status between $startDateTimeUtc and $endDateTimeUtc"
        )

        // 購入日時が昨日の0時から23時59分59秒までのチェキのPurchasedItemを取得
        val purchasedItems =
            PurchasedItem.findOrderedChekiItemByTerms(startDateTimeUtc, endDateTimeUtc)

        logger.info("Found ${purchasedItems.size} purchasedItems with successful payment status")

        // CSVに入れるデータを作成
        val csvContent = buildCsvContent(purchasedItems)

        val tempFile = Files.createTempFile("cheki_purchase_data", ".csv")
        Files.write(tempFile, csvContent.toByteArray())

        val dateStr = DateTimeFormatter.ofPattern("yyyyMMdd").format(startTimeJst)
        val dateDir = "${startTimeJst.year}/${startTimeJst.monthValue}/${startTimeJst.dayOfMonth}/"
        val remotePath = "${config.envKind()}/${dateDir}cheki_purchase_data$dateStr.csv"

        try {
            // S3へのアップロード
            uploadFileToS3(remotePath, tempFile).getOrElse { e ->
                logger.error("Failed to upload file to S3: $remotePath", e)
            }
            logger.info("Finish uploading CSV to S3")

            // smapo GCPへアップロード
            val gcpPath = remotePath.substringAfter("${config.envKind()}/")
            uploadFileToSmapo(gcpPath, tempFile).getOrElse { e ->
                logger.error("Failed to upload file to GCP Storage: $gcpPath", e)
            }
            logger.info(
                "Finish uploading CSV to GCP Storage for Smapo: ${shopConfig.smapoGcpBucketName()}/fanme/${dateDir}cheki_purchase_data$dateStr.csv"
            )
        } finally {
            try {
                Files.delete(tempFile)
            } catch (e: Exception) {
                logger.warn("Failed to delete temporary file", e)
            }
        }

        logger.info("OrderDeliveryInfoCsvBatch end")
    }

    private fun buildCsvContent(purchasedItems: List<PurchasedItem>): String {
        val header = "オーダーID,商品ID,商品タイプ,商品名,購入数,商品単価,合計,購入日時,名前（購入者）,郵便番号（購入者）,住所（購入者）,電話番号（購入者）"
        val customers =
            fanmeCustomerController.listFanmeCustomers(
                purchasedItems.map { it.purchaserUid }.distinct()
            )

        return purchasedItems
            .filter { item ->
                // 【prefix】がついたものはチェキのCSV出力対象外
                Const.PhysicalItemCost.entries
                    .mapNotNull { it.prefix }
                    .none { prefix -> item.item.name.contains("【$prefix】") }
            }
            .mapNotNull { item ->
                customers
                    .firstOrNull { it.creatorUid == item.purchaserUid }
                    ?.let { customer ->
                        listOf(
                                item.order.id,
                                item.item.id,
                                "チェキ",
                                item.item.name,
                                item.quantity,
                                item.item.price,
                                item.item.price * item.quantity,
                                item.purchasedAt?.let { Util.toJSTLocalDateTimeString(it) } ?: "",
                                "${customer.lastName} ${customer.firstName}",
                                customer.postalCode,
                                "${customer.prefecture}${customer.city}${customer.street}${customer.building ?: ""}",
                                // 先頭の0が消えないように
                                "'${customer.phoneNumber}",
                            )
                            .joinToString(",")
                    }
                    ?: run {
                        logger.warn(
                            "Customer information not found for purchaserUid: ${item.purchaserUid}"
                        )
                        null
                    }
            }
            .let { rows -> listOf(header, rows.joinToString("\n")).joinToString("\n") }
    }

    private fun uploadFileToS3(remotePath: String, filePath: Path): Result<String, Exception> {
        return try {
            s3.putObject(shopConfig.s3BucketNameForSmapo(), remotePath, "text/csv", filePath)
            Ok(remotePath)
        } catch (e: Exception) {
            Err(e)
        }
    }

    private fun uploadFileToSmapo(fileName: String, filePath: Path): Result<String, Exception> {
        try {
            Files.newInputStream(filePath).use { inputStream ->
                gcpStorageClient.uploadObject(
                    bucket = shopConfig.smapoGcpBucketName(),
                    name = "fanme/$fileName",
                    uploadType = "media",
                    content = inputStream,
                )
                return Ok(fileName)
            }
        } catch (e: Exception) {
            return Err(e)
        }
    }
}
