package jp.co.torihada.fanme.modules.payment.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.payment.usecases.monthlysales.GetMonthlySellerSales
import jp.co.torihada.fanme.modules.payment.utils.YearMonth

@ApplicationScoped
class MonthlySellerSalesController {
    @Inject private lateinit var getMonthlySellerSales: GetMonthlySellerSales

    fun getMonthlySales(
        sellerUserIds: List<String>,
        fromYearMonth: String?,
        toYearMonth: String?,
    ): GetMonthlySellerSales.Output {
        validateYearMonths(fromYearMonth, toYearMonth)

        return getMonthlySellerSales
            .execute(GetMonthlySellerSales.Input(sellerUserIds, fromYearMonth, toYearMonth))
            .getOrThrow()
    }

    private fun validateYearMonths(fromYearMonth: String?, toYearMonth: String?) {
        fromYearMonth?.let { YearMonth.validate(it) }
        toYearMonth?.let { YearMonth.validate(it) }
    }
}
