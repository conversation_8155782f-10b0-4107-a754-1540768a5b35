package jp.co.torihada.fanme.endpoints.shop

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonProperty
import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.CartItemsResponseBody
import jp.co.torihada.fanme.endpoints.BaseCreatorAccountIdentityEndpoint
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.lib.MaskConfig
import jp.co.torihada.fanme.modules.shop.controllers.CartItemController
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema

@Path("/shops/{creator_account_identity}/cart-items")
class CartItemEndpoint : BaseCreatorAccountIdentityEndpoint() {

    @Inject lateinit var securityIdentity: SecurityIdentity
    @Inject lateinit var requestContext: ContainerRequestContext
    @Inject lateinit var handler: CartItemController
    @Inject lateinit var util: Util
    @Inject lateinit var maskConfig: MaskConfig

    @GET
    @RolesAllowed("LoginUser")
    @Produces("application/json")
    @APIResponse(responseCode = "200")
    @APIResponseSchema(CartItemsResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getCartItems(): Response {
        return try {
            val creatorUid = util.getCreatorUid(requestContext)
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result = handler.getCartItems(userUid, creatorUid)
            val entity = ResponseEntity(result, "cart")

            maskConfig.enabled = true
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    data class CreateCartItemRequest(
        @JsonProperty("item_id") val itemId: Long,
        @JsonProperty("single_file_id") val singleFile: Long?,
        @JsonProperty("quantity") val quantity: Int,
    )

    @POST
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun createCartItem(@JsonProperty requestBody: CreateCartItemRequest): Response {
        return try {
            val creatorUid = util.getCreatorUid(requestContext)
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result =
                handler.createCartItem(
                    userUid,
                    creatorUid,
                    requestBody.itemId,
                    requestBody.singleFile,
                    requestBody.quantity,
                )
            val entity = ResponseEntity(result, "cart_item")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    data class UpdateCartItemRequest(
        @JsonProperty("quantity") val quantity: Int?,
        @JsonProperty("purchaser_comment")
        @JsonAlias("purchaserComment")
        val purchaserComment: String?,
    )

    @PUT
    @Path("/{cart_item_id}")
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun updateCartItem(
        @PathParam("cart_item_id") cartItemId: Long,
        requestBody: UpdateCartItemRequest,
    ): Response {
        return try {
            val creatorUid = util.getCreatorUid(requestContext)
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            Response.ok(
                    handler.updateCartItem(
                        userUid,
                        creatorUid,
                        cartItemId,
                        requestBody.quantity,
                        requestBody.purchaserComment,
                    )
                )
                .build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @DELETE
    @Path("/{cart_item_id}")
    @RolesAllowed("LoginUser")
    fun deleteCartItem(@PathParam("cart_item_id") cartItemId: Long): Response {
        return try {
            val creatorUid = util.getCreatorUid(requestContext)
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result = handler.deleteCartItem(userUid, creatorUid, cartItemId)
            if (!result) {
                return Response.serverError().build()
            }
            Response.ok().build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    data class CheckCartItemPriceRequest(
        @JsonProperty("item_prices") val itemPrices: List<ItemPriceSet>
    )

    data class ItemPriceSet(
        @JsonProperty("cart_item_id") val cartItemId: Long,
        @JsonProperty("displayed_price") val displayedPrice: Int,
    )

    @POST
    @Path("/check-price")
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    fun checkCartItemPrice(requestBody: CheckCartItemPriceRequest): Response {
        return try {
            val creatorUid = util.getCreatorUid(requestContext)
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result =
                handler.checkCartItemPrice(
                    userUid,
                    creatorUid,
                    requestBody.itemPrices.map {
                        CartItemController.ItemPrice(it.cartItemId, it.displayedPrice)
                    },
                )
            val entity = ResponseEntity(result, "result")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @DELETE
    @Path("/invalid-items")
    @RolesAllowed("LoginUser")
    @Produces(MediaType.APPLICATION_JSON)
    fun getInvalidCartItems(): Response {
        return try {
            val creatorUid = util.getCreatorUid(requestContext)
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result = handler.deleteInvalidCartItems(creatorUid, userUid)
            val entity = ResponseEntity(result, "deleted_cart_items")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
