package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.time.YearMonth
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.utils.SecurityUtils
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.payment.controllers.MonthlySellerSalesController
import jp.co.torihada.fanme.modules.payment.controllers.TransactionController
import jp.co.torihada.fanme.modules.payment.models.Transaction

@ApplicationScoped
class GetAgencySales
@Inject
constructor(
    private val monthlySellerSalesController: MonthlySellerSalesController,
    private val transactionController: TransactionController,
    private val securityUtils: SecurityUtils,
    private val userController: UserController,
) {

    data class Input(
        val agencyId: Long,
        val currentUserUid: String,
        val currentUserRole: ConsoleUserRole,
    )

    data class MonthlySales(
        val yearMonth: String,
        val sellerSalesAmount: Int,
        val purchaserCount: Int,
        val purchaseCount: Int,
    )

    data class UserSales(
        val userUid: String,
        val userName: String,
        val thisMonth: MonthlySales?,
        val lastMonth: MonthlySales?,
    )

    data class TotalSales(val thisMonth: MonthlySales?, val lastMonth: MonthlySales?)

    data class AgencySales(val totalSales: TotalSales, val usersSales: List<UserSales>)

    data class Output(val agencySales: AgencySales)

    fun execute(input: Input): Result<Output, FanmeException> {
        val agency = Agency.findById(input.agencyId)
        if (agency == null || agency.deletedAt != null) {
            return Err(ResourceNotFoundException("Agency not found"))
        }

        val currentConsoleUser = ConsoleUser.findByUserUid(input.currentUserUid)

        securityUtils.validateAgentAccess(
            input.agencyId,
            input.currentUserRole.value,
            currentConsoleUser?.agencyId,
        )

        val consoleUsers = ConsoleUser.findByAgencyId(input.agencyId)

        val userUids = consoleUsers.mapNotNull { consoleUser -> consoleUser.user.uid }

        if (userUids.isEmpty()) {
            val emptyAgencySales =
                AgencySales(
                    totalSales = TotalSales(thisMonth = null, lastMonth = null),
                    usersSales = emptyList(),
                )
            return Ok(Output(emptyAgencySales))
        }

        val currentYearMonth = YearMonth.now()
        val lastYearMonth = currentYearMonth.minusMonths(1)
        val thisMonthStr = currentYearMonth.toString().replace("-", "")
        val lastMonthStr = lastYearMonth.toString().replace("-", "")

        val monthlySalesResponse =
            monthlySellerSalesController.getMonthlySales(userUids, lastMonthStr, thisMonthStr)

        val thisMonthTransactions =
            transactionController.getSellerTransactions(userUids, thisMonthStr)
        val thisMonthPurchaseStats = calculateSellerPurchaseStats(thisMonthTransactions)

        val lastMonthTransactions =
            transactionController.getSellerTransactions(userUids, lastMonthStr)
        val lastMonthPurchaseStats = calculateSellerPurchaseStats(lastMonthTransactions)

        val users = userController.getUsers(userUids)
        val userIdToNameMap = users.associate { user -> user.uid to user.displayName }

        val salesByUser = monthlySalesResponse.monthlySalesList.groupBy { it.sellerUserId }
        val thisMonthStatsByUser = thisMonthPurchaseStats.associateBy { it.sellerUserId }
        val lastMonthStatsByUser = lastMonthPurchaseStats.associateBy { it.sellerUserId }

        val userSalesDataList =
            userUids.map { userId ->
                val userSalesList = salesByUser[userId] ?: emptyList()
                val thisMonthSales = userSalesList.find { it.yearMonth == thisMonthStr }
                val lastMonthSales = userSalesList.find { it.yearMonth == lastMonthStr }
                val thisMonthStats = thisMonthStatsByUser[userId]
                val lastMonthStats = lastMonthStatsByUser[userId]

                UserSales(
                    userUid = userId,
                    userName = userIdToNameMap[userId] ?: "FANMEユーザー",
                    thisMonth =
                        thisMonthSales?.let {
                            MonthlySales(
                                yearMonth = thisMonthStr,
                                sellerSalesAmount = it.sellerSalesAmount,
                                purchaserCount = thisMonthStats?.purchaserCount ?: 0,
                                purchaseCount = thisMonthStats?.purchaseCount ?: 0,
                            )
                        },
                    lastMonth =
                        lastMonthSales?.let {
                            MonthlySales(
                                yearMonth = lastMonthStr,
                                sellerSalesAmount = it.sellerSalesAmount,
                                purchaserCount = lastMonthStats?.purchaserCount ?: 0,
                                purchaseCount = lastMonthStats?.purchaseCount ?: 0,
                            )
                        },
                )
            }

        val totalSales = calculateTotalSales(userSalesDataList)

        val agencySales = AgencySales(totalSales = totalSales, usersSales = userSalesDataList)

        return Ok(Output(agencySales))
    }

    private fun calculateTotalSales(userSalesList: List<UserSales>): TotalSales {
        val thisMonthTotal = calculateMonthTotal(userSalesList) { it.thisMonth }
        val lastMonthTotal = calculateMonthTotal(userSalesList) { it.lastMonth }
        return TotalSales(thisMonth = thisMonthTotal, lastMonth = lastMonthTotal)
    }

    private fun calculateMonthTotal(
        userSalesList: List<UserSales>,
        monthSelector: (UserSales) -> MonthlySales?,
    ): MonthlySales? {
        return if (userSalesList.any { monthSelector(it) != null }) {
            val yearMonth = userSalesList.firstNotNullOf { monthSelector(it) }.yearMonth
            val totalSalesAmount = userSalesList.sumOf { monthSelector(it)?.sellerSalesAmount ?: 0 }
            val totalPurchaseCount = userSalesList.sumOf { monthSelector(it)?.purchaseCount ?: 0 }
            val totalPurchaserCount = userSalesList.sumOf { monthSelector(it)?.purchaserCount ?: 0 }

            MonthlySales(
                yearMonth = yearMonth,
                sellerSalesAmount = totalSalesAmount,
                purchaserCount = totalPurchaserCount,
                purchaseCount = totalPurchaseCount,
            )
        } else null
    }

    private data class SellerPurchaseStats(
        val sellerUserId: String,
        val purchaserCount: Int,
        val purchaseCount: Int,
    )

    private fun calculateSellerPurchaseStats(
        transactions: List<Transaction>
    ): List<SellerPurchaseStats> {
        return transactions
            .groupBy { it.sellerUserId }
            .map { (sellerUserId, sellerTransactions) ->
                SellerPurchaseStats(
                    sellerUserId = sellerUserId ?: "",
                    purchaserCount = sellerTransactions.map { it.purchaserUserId }.distinct().size,
                    purchaseCount = sellerTransactions.size,
                )
            }
    }
}
