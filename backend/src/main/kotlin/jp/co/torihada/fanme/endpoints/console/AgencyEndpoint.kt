package jp.co.torihada.fanme.endpoints.console

import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.GET
import jakarta.ws.rs.Path
import jakarta.ws.rs.PathParam
import jakarta.ws.rs.Produces
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.AgenciesResponseBody
import jp.co.torihada.fanme.dto.AgencySalesResponseBody
import jp.co.torihada.fanme.dto.UsersResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.ConsoleException
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ForbiddenAccessException
import jp.co.torihada.fanme.exception.InvalidYearMonthFormatException
import jp.co.torihada.fanme.exception.PaymentServiceErrorException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.controllers.AgencyController
import jp.co.torihada.fanme.odata.OData
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema
import org.eclipse.microprofile.openapi.annotations.tags.Tag

@Path("/console/agencies")
@Tag(name = "CONSOLE", description = "CONSOLE APIサーバー")
class AgencyEndpoint {

    @Inject private lateinit var handler: AgencyController

    @Inject private lateinit var requestContext: ContainerRequestContext

    @Inject private lateinit var securityIdentity: SecurityIdentity

    @Inject private lateinit var util: Util

    @GET
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    @APIResponse(responseCode = "200", description = "成功")
    @APIResponseSchema(AgenciesResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    @Produces(MediaType.APPLICATION_JSON)
    fun getAgencies(): Response {
        return try {
            val odata = requestContext.getProperty("odata") as OData?
            val currentUserUid = util.getCurrentUserUid(securityIdentity)
            val currentUserRole = util.getCurrentUserRole(securityIdentity)

            if (currentUserUid == null || currentUserRole == null) {
                return Response.status(Response.Status.UNAUTHORIZED).build()
            }

            val result = handler.getAgencies(currentUserUid, currentUserRole, odata)
            val entity = ResponseEntity(result, "agencies")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @GET
    @Path("/{agency_id}/users")
    @Produces(MediaType.APPLICATION_JSON)
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    @APIResponse(responseCode = "200", description = "成功")
    @APIResponseSchema(
        value = UsersResponseBody::class,
        responseCode = "200",
        responseDescription = "成功",
    )
    @APIResponse(responseCode = "400", description = "Bad Request")
    @APIResponse(responseCode = "401", description = "Unauthorized")
    @APIResponse(responseCode = "403", description = "Forbidden")
    @APIResponse(responseCode = "404", description = "Not Found")
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getAgencyUsers(@PathParam("agency_id") agencyId: Long): Response {
        try {
            val currentUserUid = util.getCurrentUserUid(securityIdentity)
            val currentUserRole = util.getCurrentUserRole(securityIdentity)

            if (currentUserUid == null || currentUserRole == null) {
                return Response.status(Response.Status.UNAUTHORIZED).build()
            }

            val users = handler.getAgencyUsers(agencyId, currentUserUid, currentUserRole)
            val entity = ResponseEntity(users, "users")
            return Response.ok(entity).build()
        } catch (e: ConsoleException) {
            return handleConsoleException(e)
        } catch (e: Exception) {
            return Response.serverError().entity(e).build()
        }
    }

    @GET
    @Path("/{agency_id}/sales")
    @Produces(MediaType.APPLICATION_JSON)
    @RolesAllowed(UserRole.SUPER_VALUE, UserRole.BIZ_VALUE, UserRole.AGENT_VALUE)
    @APIResponse(responseCode = "200", description = "成功")
    @APIResponseSchema(
        value = AgencySalesResponseBody::class,
        responseCode = "200",
        responseDescription = "成功",
    )
    @APIResponse(responseCode = "400", description = "Bad Request")
    @APIResponse(responseCode = "401", description = "Unauthorized")
    @APIResponse(responseCode = "403", description = "Forbidden")
    @APIResponse(responseCode = "404", description = "Not Found")
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getAgencySales(@PathParam("agency_id") agencyId: Long): Response {

        try {
            val currentUserUid = util.getCurrentUserUid(securityIdentity)
            val currentUserRole = util.getCurrentUserRole(securityIdentity)

            if (currentUserUid == null || currentUserRole == null) {
                return Response.status(Response.Status.UNAUTHORIZED).build()
            }

            val agencySales = handler.getAgencySales(agencyId, currentUserUid, currentUserRole)

            val entity = ResponseEntity(agencySales, "agencySales")
            return Response.ok(entity).build()
        } catch (e: Exception) {
            val status =
                when (e) {
                    is InvalidYearMonthFormatException -> Response.Status.BAD_REQUEST
                    is ForbiddenAccessException -> Response.Status.FORBIDDEN
                    is ResourceNotFoundException -> Response.Status.NOT_FOUND
                    is PaymentServiceErrorException -> Response.Status.SERVICE_UNAVAILABLE
                    is ConsoleException -> Response.Status.INTERNAL_SERVER_ERROR
                    is FanmeException -> Response.Status.INTERNAL_SERVER_ERROR
                    else -> throw e
                }
            return Response.status(status).entity(e).build()
        }
    }

    private fun handleConsoleException(e: ConsoleException): Response {
        val errorObject = mapOf("code" to e.errors.firstOrNull()?.code, "message" to e.message)
        return when {
            e is ResourceNotFoundException ->
                Response.status(Response.Status.NOT_FOUND).entity(errorObject).build()
            e is ForbiddenAccessException ->
                Response.status(Response.Status.FORBIDDEN).entity(errorObject).build()
            else -> Response.status(Response.Status.BAD_REQUEST).entity(errorObject).build()
        }
    }
}
