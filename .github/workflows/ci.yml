name: TEST

on:
  pull_request:
    types: [opened, reopened, synchronize, ready_for_review]
  push:
    branches:
      - main
  workflow_dispatch:

permissions:
  contents: read
  pull-requests: read

jobs:
  check-changes:
    name: 実行するテストの選別
    runs-on: ubuntu-latest
    if: github.event_name != 'push' || !startsWith(github.event.head_commit.message, 'main branch build. Applied charts tag')
    outputs:
      frontend: ${{ steps.filter.outputs.frontend }}
      backend: ${{ steps.filter.outputs.backend }}
    steps:
      - uses: actions/checkout@v4

      - uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            frontend:
              - frontend/**
            backend:
              - backend/**

  # Githubブランチプロテクトの「Require status checks to pass」を設定したいため、
  # skipは各処理内で行う
  call-frontend-ci:
    name: frontendテスト
    needs: check-changes
    if: github.event.pull_request.draft == false
    uses: ./.github/workflows/ci_frontend.yml
    with:
      skip: ${{ needs.check-changes.outputs.frontend == 'true' && 'false' || 'true' }}
    secrets:
      AUTH_TOKEN_FOR_GITHUBPKG: ${{ secrets.AUTH_TOKEN_FOR_GITHUBPKG }}

  call-backend-ci:
    name: backendテスト
    needs: check-changes
    if: github.event.pull_request.draft == false
    uses: ./.github/workflows/ci_backend.yml
    with:
      skip: ${{ needs.check-changes.outputs.backend == 'true' && 'false' || 'true' }}
